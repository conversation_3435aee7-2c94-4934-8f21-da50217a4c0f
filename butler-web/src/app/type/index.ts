export type Conversation = {
  sender: string;
  message: string;
  time?: number | string;
  id?: string;
  suggestedQuestions?: string[];
  dishes?: Dish[];
  keywords?: string[];
  groupedDishes?: { category: string; dishes: Dish[] }[];
};

export type FoodChain = {
  categories: [];
  contact: string;
  createdAt: string;
  name: string;
  tagline?: string;
  outlets: Outlet[];
  website?: string;
  theme: ThemeConfig;
  email: string;
  updatedAt: string;
  stats: Stats;
  _id: string;
};

export type Stats = {
  totalOrders: number;
  totalRevenue: number;
  totalCustomers: number;
  totalDishes: number;
  totalCategories: number;
  avgOrderValue: number;
  totalOutlets: number;
};

export type ThemeConfig = {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  logoUrl: string;
  chainId: string;
  outletId: string;
  favIcon: string;
  name: string;
};

export type OperatingHours = {
  day:
    | "monday"
    | "tuesday"
    | "wednesday"
    | "thursday"
    | "friday"
    | "saturday"
    | "sunday";
  isOpen: boolean;
  openTime: string;
  closeTime: string;
};

export type PaymentSettings = {
  acceptCash: boolean;
  acceptOnline: boolean;
  defaultPaymentMethod: "cash" | "online";
};

export type StaffAssignment = {
  userId: string;
  role: "manager" | "chef" | "delivery" | "cashier" | "waiter";
  isActive: boolean;
  _id?: string;
};

export type Outlet = {
  name: string;
  address: string;
  city: string;
  pincode: string;
  contact: string;
  foodChain: FoodChain | string;
  status?: "active" | "inactive";
  operatingHours?: OperatingHours[];
  paymentSettings?: PaymentSettings;
  deliveryRadius?: number;
  deliveryZones?: string[];
  staff?: StaffAssignment[];
  createdAt?: string;
  updatedAt?: string;
  isCloudKitchen: boolean;
  _id?: string;
  dishes?: Dish[];
};

export type OutletPricing = {
  outletId: string;
  price: number;
};

export type TimeAvailability = {
  breakfast?: boolean;
  lunch?: boolean;
  dinner?: boolean;
  customHours?: boolean;
  startTime?: string;
  endTime?: string;
};

export type SeasonalAvailability = {
  startDate?: Date | string;
  endDate?: Date | string;
};

export type AddOn = {
  _id: string;
  name: string;
  price: number;
  type?: string;
  description?: string;
  quantity?: number;
};

export type SelectedAddOn = {
  addOnId: string;
  name: string;
  price: number;
  quantity: number;
};

export type Dish = {
  name: string;
  description?: string;
  price: number;
  image?: string;
  category: Category | string;
  cuisine?: string;
  foodChain: string;
  outlets: string[];
  outletPricing?: OutletPricing[];
  isAvailable?: boolean;
  isVeg?: boolean;
  isSeasonal?: boolean;
  isFeatured?: boolean;
  seasonalAvailability?: SeasonalAvailability;
  timeAvailability?: TimeAvailability;
  // Optional add-ons enabled for this dish
  enabledAddOns?: AddOn[];
  // Selected add-ons for cart items
  selectedAddOns?: SelectedAddOn[];
  createdAt?: string;
  updatedAt?: string;
  quantity?: number;
  _id?: string;
};

export type Category = {
  name: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
  _id?: string;
};

export type ConversationListData = {
  conversationId: string;
  outletName: string;
  outletAddress: string;
  lastMessage: {
    message: string;
    sender: string;
    time: number | string;
    suggestedQuestions: string[];
    _id: string;
  };
  messageCount: number;
  createdAt: string;
  updatedAt: string;
  foodChainId: string;
  outletImage?: string;
};

export type User = {
  name: string;
  email: string;
  phone: string;
  foodChain: string;
  role?: string;
  status?: "active" | "blocked";
  createdAt?: string;
  updatedAt?: string;
  _id?: string;
};

export type OfferType =
  | "BOGO"
  | "combo"
  | "discount"
  | "freeItem"
  | "quantityDiscount"
  | "multiDishType"
  | "minimumAmount"
  | "dayOfWeek"
  | "dateRange"
  | "customerTier"
  | "firstTime"
  | "timeBasedSpecial";

export type CustomerTier = "new" | "regular" | "vip" | "premium";

export type TimeRestrictions = {
  startTime?: string; // HH:MM format
  endTime?: string; // HH:MM format
  daysOfWeek?: number[]; // 0 = Sunday, 6 = Saturday
};

export type ComboItem = {
  dishId: string | Dish;
  quantity: number;
};

export type DiscountDetails = {
  discountType?: "percentage" | "fixed";
  discountValue?: number;
  // BOGO and quantity discount fields
  buyQuantity?: number;
  getQuantity?: number;
  // Free item fields
  freeItemId?: Dish;
  freeItemQuantity?: number;
  // Combo offer fields
  comboItems?: ComboItem[];
  comboPrice?: number;
  // Minimum amount fields
  minimumOrderValue?: number;
  maxDiscount?: number;
  // Multi-dish type requirements
  requiredCategories?: string[] | Category[];
  minimumCategoriesCount?: number;
  // Customer tier restrictions
  customerTiers?: CustomerTier[];
  // Time-based restrictions
  timeRestrictions?: TimeRestrictions;
};

export type StackingRules = {
  canStackWithOthers?: boolean;
  stackableOfferTypes?: string[];
  priority?: number; // Higher number = higher priority
};

export type UsageRules = {
  usageLimit?: number; // 0 means unlimited
  usedCount?: number;
  perCustomerLimit?: number; // 0 means unlimited per customer
  perOrderLimit?: number; // How many times this offer can be applied in a single order
};

export type OfferAnalytics = {
  totalUsage?: number;
  totalSavings?: number;
  uniqueCustomers?: string[] | User[];
};

export type Offer = {
  offerName: string;
  _id?: string;
  offerId: string;
  name: string;
  description?: string;
  offerType: OfferType;
  discountDetails: DiscountDetails;
  startDate: string | Date;
  endDate: string | Date;
  isActive?: boolean;
  createdBy?: string | User;
  foodChainId: string | FoodChain;
  applicableOutlets?: string[] | Outlet[];
  applicableDishes?: string[] | Dish[];
  termsAndConditions?: string;
  bannerImage?: string;
  displayOnApp?: boolean;
  // Offer stacking and usage rules
  stackingRules?: StackingRules;
  usageRules?: UsageRules;
  // Auto-application settings
  autoApply?: boolean;
  // Analytics tracking
  analytics?: OfferAnalytics;
  createdAt?: string;
  updatedAt?: string;
  // Virtual fields
  isExpired?: boolean;
  isValid?: boolean;
  isUsageLimitReached?: boolean;
  isTimeRestricted?: boolean;
  // API response fields
  canAutoApply?: boolean;
  estimatedSavings?: number;
};
