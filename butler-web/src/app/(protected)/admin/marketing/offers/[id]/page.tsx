/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { ArrowLeft, Loader2, Save } from "lucide-react";
import { getOfferById, updateOffer } from "@/server/marketing";
import { getAllOutlets, getAllDishes } from "@/server/admin";
import { useRouter, useParams } from "next/navigation";

export default function OfferEditPage() {
  const { id } = useParams() || {};
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [offer, setOffer] = useState<any>(null);
  const [outlets, setOutlets] = useState([]);
  const [dishes, setDishes] = useState([]);

  // Fetch offer details
  const fetchOfferDetails = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getOfferById(id as string);
      if (response.success) {
        setOffer({
          ...response.data,
          startDate: new Date(response.data.startDate),
          endDate: new Date(response.data.endDate),
          applicableOutlets:
            response.data.applicableOutlets?.map((o: any) => o._id) || [],
          applicableDishes:
            response.data.applicableDishes?.map((d: any) => d._id) || [],
        });
      } else {
        toast.error(response.message || "Failed to fetch offer details");
        router.push("/admin/marketing/offers");
      }
    } catch (error) {
      console.error("Error fetching offer details:", error);
      toast.error("An error occurred while fetching offer details");
      router.push("/admin/marketing/offers");
    } finally {
      setLoading(false);
    }
  }, [id, router]);

  // Fetch outlets and dishes
  const fetchOutletsAndDishes = useCallback(async () => {
    try {
      const outletsResponse = await getAllOutlets();
      if (outletsResponse.success) {
        setOutlets(outletsResponse.data);
      }

      const dishesResponse = await getAllDishes();
      if (dishesResponse.success) {
        setDishes(dishesResponse.data);
      }
    } catch (error) {
      console.error("Error fetching outlets and dishes:", error);
    }
  }, []);

  useEffect(() => {
    fetchOfferDetails();
    fetchOutletsAndDishes();
  }, [fetchOfferDetails, fetchOutletsAndDishes]);

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setOffer({ ...offer, [name]: value });
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setOffer({ ...offer, [name]: value });
  };

  // Handle date changes
  const handleDateChange = (name: string, date: Date | undefined) => {
    if (date) {
      setOffer({ ...offer, [name]: date });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setOffer({ ...offer, [name]: checked });
  };

  // Handle outlet selection
  const handleOutletSelection = (outletId: string, checked: boolean) => {
    setOffer((prev: any) => {
      const outlets = checked
        ? [...prev.applicableOutlets, outletId]
        : prev.applicableOutlets.filter((id: string) => id !== outletId);
      return { ...prev, applicableOutlets: outlets };
    });
  };

    const handleDayOfWeekSelection = (day: number, checked: boolean) => {
    setOffer((prev: any) => {
      const days = checked
        ? [...prev.discountDetails.timeRestrictions.daysOfWeek, day]
        : prev.discountDetails.timeRestrictions.daysOfWeek.filter(
            (d: number) => d !== day
          );
      return {
        ...prev,
        discountDetails: {
          ...prev.discountDetails,
          timeRestrictions: {
            ...prev.discountDetails.timeRestrictions,
            daysOfWeek: days,
          },
        },
      };
    });
  };

  // Handle dish selection
  const handleDishSelection = (dishId: string, checked: boolean) => {
    setOffer((prev: any) => {
      const dishes = checked
        ? [...prev.applicableDishes, dishId]
        : prev.applicableDishes.filter((id: string) => id !== dishId);
      return { ...prev, applicableDishes: dishes };
    });
  };

    const handleNestedChange = (path: string, value: any) => {
    const keys = path.split(".");
    const newOffer = { ...offer };
    let current = newOffer;

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;

    setOffer(newOffer);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!offer.name) {
      toast.error("Offer name is required");
      return;
    }

    setSaving(true);
    try {
      const response = await updateOffer(id as string, offer);
      if (response.success) {
        toast.success("Offer updated successfully");
        router.push("/admin/marketing/offers");
      } else {
        toast.error(response.message || "Failed to update offer");
      }
    } catch (error) {
      console.error("Error updating offer:", error);
      toast.error("An error occurred while updating the offer");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  if (!offer) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium">Offer not found</h3>
        <Button
          className="mt-4"
          onClick={() => router.push("/admin/marketing/offers")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Offers
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.push("/admin/marketing/offers")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Offers
        </Button>
        <Button onClick={handleSubmit} disabled={saving}>
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </>
          )}
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Edit Offer</h2>

        <div className="grid gap-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Offer Name</Label>
              <Input
                id="name"
                name="name"
                placeholder="e.g., Summer Special"
                value={offer.name}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="offerType">Offer Type</Label>
              <Select
                value={offer.offerType}
                onValueChange={(value) =>
                  handleSelectChange("offerType", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select offer type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BOGO">Buy One Get One</SelectItem>
                  <SelectItem value="combo">Combo Offer</SelectItem>
                  <SelectItem value="discount">Discount</SelectItem>
                  <SelectItem value="freeItem">Free Item</SelectItem>
                  <SelectItem value="quantityDiscount">
                        Quantity Discount
                      </SelectItem>
                      <SelectItem value="multiDishType">
                        Multi-Dish Type
                      </SelectItem>
                      <SelectItem value="minimumAmount">
                        Minimum Amount
                      </SelectItem>
                      <SelectItem value="dayOfWeek">Day of Week</SelectItem>
                      <SelectItem value="dateRange">Date Range</SelectItem>
                      <SelectItem value="customerTier">
                        Customer Tier
                      </SelectItem>
                      <SelectItem value="firstTime">First Time</SelectItem>
                      <SelectItem value="timeBasedSpecial">
                        Time Based Special
                      </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Describe the offer"
              value={offer.description || ""}
              onChange={handleInputChange}
              className="min-h-[100px]"
            />
          </div>

          {/* Discount Details */}
          {offer.offerType && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Discount Details</h3>

              {/* Common discount fields for applicable offer types */}
              {[
                "discount",
                "minimumAmount",
                "quantityDiscount",
                "dateRange",
                "customerTier",
                "firstTime",
                "timeBasedSpecial",
                "dayOfWeek",
              ].includes(offer.offerType) && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Discount Type</Label>
                    <Select
                      value={
                        offer.discountDetails?.discountType || "percentage"
                      }
                      onValueChange={(value) =>
                        setOffer({
                          ...offer,
                          discountDetails: {
                            ...offer.discountDetails,
                            discountType: value,
                          },
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="percentage">Percentage</SelectItem>
                        <SelectItem value="fixed">Fixed Amount</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Discount Value</Label>
                    <Input
                      type="number"
                      placeholder={
                        offer.discountDetails?.discountType === "percentage"
                          ? "e.g., 10"
                          : "e.g., 100"
                      }
                      value={offer.discountDetails?.discountValue || ""}
                      onChange={(e) =>
                        setOffer({
                          ...offer,
                          discountDetails: {
                            ...offer.discountDetails,
                            discountValue: Number(e.target.value),
                          },
                        })
                      }
                    />
                  </div>
                  {offer.discountDetails?.discountType === "percentage" && (
                    <div className="space-y-2">
                      <Label>Max Discount (₹)</Label>
                      <Input
                        type="number"
                        placeholder="e.g., 500"
                        value={offer.discountDetails?.maxDiscount || ""}
                        onChange={(e) =>
                          setOffer({
                            ...offer,
                            discountDetails: {
                              ...offer.discountDetails,
                              maxDiscount: Number(e.target.value),
                            },
                          })
                        }
                      />
                    </div>
                  )}
                </div>
              )}

              {/* Combo offer fields */}
              {offer.offerType === "combo" && (
                <div className="space-y-2">
                  <Label>Combo Price (₹)</Label>
                  <Input
                    type="number"
                    placeholder="e.g., 299"
                    value={offer.discountDetails?.comboPrice || ""}
                    onChange={(e) =>
                      setOffer({
                        ...offer,
                        discountDetails: {
                          ...offer.discountDetails,
                          comboPrice: Number(e.target.value),
                        },
                      })
                    }
                  />
                </div>
              )}

              {/* Minimum amount fields */}
              {offer.offerType === "minimumAmount" && (
                <div className="space-y-2">
                  <Label>Minimum Order Value (₹)</Label>
                  <Input
                    type="number"
                    placeholder="e.g., 500"
                    value={offer.discountDetails?.minimumOrderValue || ""}
                    onChange={(e) =>
                      setOffer({
                        ...offer,
                        discountDetails: {
                          ...offer.discountDetails,
                          minimumOrderValue: Number(e.target.value),
                        },
                      })
                    }
                  />
                </div>
              )}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Start Date</Label>
              <DatePicker
                date={offer.startDate}
                setDate={(date) => handleDateChange("startDate", date)}
              />
            </div>
            <div className="space-y-2">
              <Label>End Date</Label>
              <DatePicker
                date={offer.endDate}
                setDate={(date) => handleDateChange("endDate", date)}
              />
            </div>
          </div>
           {["dayOfWeek", "timeBasedSpecial"].includes(
                  offer.offerType
                ) && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Start Time</Label>
                        <Input
                          type="time"
                          value={
                            offer.discountDetails.timeRestrictions.startTime
                          }
                          onChange={(e) =>
                            handleNestedChange(
                              "discountDetails.timeRestrictions.startTime",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>End Time</Label>
                        <Input
                          type="time"
                          value={offer.discountDetails.timeRestrictions.endTime}
                          onChange={(e) =>
                            handleNestedChange(
                              "discountDetails.timeRestrictions.endTime",
                              e.target.value
                            )
                          }
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Days of Week</Label>
                      <div className="grid grid-cols-7 gap-2">
                        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
                          (day, index) => (
                            <div
                              key={day}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`day-${index}`}
                                checked={offer.discountDetails.timeRestrictions.daysOfWeek.includes(
                                  index
                                )}
                                onCheckedChange={(checked) =>
                                  handleDayOfWeekSelection(
                                    index,
                                    checked === true
                                  )
                                }
                              />
                              <Label
                                htmlFor={`day-${index}`}
                                className="text-sm"
                              >
                                {day}
                              </Label>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  </div>
                )}

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              checked={offer.isActive}
              onCheckedChange={(checked) =>
                handleCheckboxChange("isActive", checked === true)
              }
            />
            <Label htmlFor="isActive">Active</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="displayOnApp"
              checked={offer.displayOnApp}
              onCheckedChange={(checked) =>
                handleCheckboxChange("displayOnApp", checked === true)
              }
            />
            <Label htmlFor="displayOnApp">Display on App</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="autoApply"
              checked={offer.autoApply}
              onCheckedChange={(checked) =>
                handleCheckboxChange("autoApply", checked === true)
              }
            />
            <Label htmlFor="autoApply">Auto Apply</Label>
            <p className="text-xs text-gray-500 ml-2">
              Automatically apply this offer when conditions are met
            </p>
          </div>

          <div className="space-y-2">
            <Label>Applicable Outlets</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-2">
              {outlets.map((outlet: any) => (
                <div key={outlet._id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`outlet-${outlet._id}`}
                    checked={offer.applicableOutlets.includes(outlet._id)}
                    onCheckedChange={(checked) =>
                      handleOutletSelection(outlet._id, checked === true)
                    }
                  />
                  <Label htmlFor={`outlet-${outlet._id}`} className="text-sm">
                    {outlet.address}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500">
              If none selected, offer applies to all outlets
            </p>
          </div>

          <div className="space-y-2">
            <Label>Applicable Dishes</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-2">
              {dishes.map((dish: any) => (
                <div key={dish._id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`dish-${dish._id}`}
                    checked={offer.applicableDishes.includes(dish._id)}
                    onCheckedChange={(checked) =>
                      handleDishSelection(dish._id, checked === true)
                    }
                  />
                  <Label htmlFor={`dish-${dish._id}`} className="text-sm">
                    {dish.name}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500">
              If none selected, offer applies to all dishes
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="termsAndConditions">Terms and Conditions</Label>
            <Textarea
              id="termsAndConditions"
              name="termsAndConditions"
              placeholder="Enter terms and conditions"
              value={offer.termsAndConditions || ""}
              onChange={handleInputChange}
              className="min-h-[100px]"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
