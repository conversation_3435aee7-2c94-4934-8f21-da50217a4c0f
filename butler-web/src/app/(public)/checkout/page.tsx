"use client";
// Removed unused eslint-disable directive
import useBackendCart from "@/hooks/useBackendCart";
import React, {
  Suspense,
  useState,
  useEffect,
  // useMemo, // Unused for now
  // useCallback, // Unused for now
} from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTheme } from "@/contexts/ThemeContext";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  ArrowLeft,
  ShoppingBag,
  Clock,
  CheckCircle2,
  AlertCircle,
  Edit3,
  Tag,
  X,
  Loader2,
} from "lucide-react";
import { useSocket } from "@/contexts/SocketContext";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
// import ApplicableOffers from "@/components/custom/offers/ApplicableOffers"; // Temporarily disabled
import { Dish } from "@/app/type";
// import {
//   calculateTotalWithOffers,
//   CartItem,
//   OfferCalculationResult,
// } from "@/utils/offerCalculations"; // Unused for now

const CheckoutPageWrapper = () => {
  return (
    <Suspense>
      {" "}
      <UserCheckoutPage />
    </Suspense>
  );
};

const UserCheckoutPage = () => {
  const {
    cart,
    appliedOffers,
    cartTotals,
    // updateItemQuantity, // Unused for now
    removeFromCart: removeFromCartBackend,
    addToCart: addToCartBackend,
    clearCart,
    // refreshCart, // Unused for now
  } = useBackendCart();
  const [pageLoading, setPageLoading] = useState(true);
  const params = useSearchParams();
  const { theme } = useTheme();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [couponLoading, setCouponLoading] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    discountType: string;
    discountValue: number;
  } | null>(null);

  // Addon editing state
  const [editingItem, setEditingItem] = useState<Dish | null>(null);
  const [isAddonDialogOpen, setIsAddonDialogOpen] = useState(false);
  const [selectedAddOns, setSelectedAddOns] = useState<
    Array<{
      addOnId: string;
      name: string;
      price: number;
      quantity: number;
    }>
  >([]);

  useEffect(() => {
    if (cart || cartTotals) setPageLoading(false);
  }, [cart, cartTotals]);
  // Handle addon editing
  const handleEditAddons = (item: Dish) => {
    setEditingItem(item);
    setSelectedAddOns(item.selectedAddOns || []);
    setIsAddonDialogOpen(true);
  };

  const handleSaveAddons = async () => {
    if (!editingItem) return;

    try {
      // Remove the existing item and re-add with new addons
      await removeFromCartBackend(editingItem._id!);

      // Format addons for backend
      const formattedAddOns = selectedAddOns.map((addon) => ({
        addOnId: addon.addOnId,
        name: addon.name,
        price: addon.price,
        quantity: addon.quantity,
      }));

      // Re-add item with new addons
      await addToCartBackend(
        editingItem,
        editingItem.quantity,
        formattedAddOns
      );

      setIsAddonDialogOpen(false);
      setEditingItem(null);
      setSelectedAddOns([]);
      toast.success("Add-ons updated successfully");
    } catch (error) {
      console.error("Error updating addons:", error);
      toast.error("Failed to update add-ons");
    }
  };

  const { customerSocket } = useSocket();

  // Check if we're updating an existing order
  const orderId = params?.get("orderId");
  const isUpdatingOrder = !!orderId;

  // Fix mobile scrolling issues
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const setViewportHeight = () => {
      // Clear any existing timeout to prevent memory leaks
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Set CSS custom properties for viewport height
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", `${vh}px`);
      document.documentElement.style.setProperty(
        "--app-height",
        `${window.innerHeight}px`
      );
    };

    const handleResize = () => {
      // Debounce resize events to prevent excessive calls
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(setViewportHeight, 100);
    };

    const handleOrientationChange = () => {
      // Delay to ensure proper viewport calculation after orientation change
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(setViewportHeight, 200);
    };

    // Force body to be scrollable immediately
    const forceScrollable = () => {
      if (typeof window !== "undefined") {
        // Remove any conflicting classes
        document.body.classList.remove("chat-page-body");

        // Force scrollable styles
        document.body.style.overflow = "auto";
        document.body.style.height = "auto";
        document.body.style.minHeight = "100vh";
        document.body.style.position = "static";
        document.documentElement.style.overflow = "auto";
        document.documentElement.style.height = "auto";
        document.documentElement.style.minHeight = "100vh";

        // Force a reflow to ensure styles are applied
        void document.body.offsetHeight;
      }
    };

    // Apply scrollable styles immediately
    forceScrollable();

    // Set initial viewport height
    setViewportHeight();

    // Add event listeners with passive option for better performance
    window.addEventListener("resize", handleResize, { passive: true });
    window.addEventListener("orientationchange", handleOrientationChange, {
      passive: true,
    });

    // Additional check after a short delay to ensure everything is applied
    const finalCheck = setTimeout(forceScrollable, 100);

    return () => {
      // Clean up all timeouts and event listeners
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      clearTimeout(finalCheck);
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("orientationchange", handleOrientationChange);
    };
  }, []);

  // Get user data from localStorage if available
  const getUserData = () => {
    if (typeof window !== "undefined") {
      const userData = localStorage.getItem("user-data");
      return userData ? JSON.parse(userData) : null;
    }
    return null;
  };

  const userData = getUserData();

  // Form data with user information if available
  const [formData, setFormData] = useState({
    name: userData?.name || "",
    phone: userData?.phone || "",
    email: userData?.email || "",
    address: userData?.address || "",
    paymentMethod: "cash",
    specialInstructions: "",
    tableNumber: "",
  });

  // Use backend cart totals
  const subtotal = cartTotals.subtotal;
  // Use backend cart offer calculations
  const offerDiscount = cartTotals.totalOfferDiscount;

  // Calculate total with both offer and coupon discounts
  const couponDiscount = appliedCoupon ? appliedCoupon.discount : 0;
  const totalDiscount = offerDiscount + couponDiscount;
  const total = Math.max(0, subtotal - totalDiscount);

  // Memoize orderData and appliedOfferIds to prevent unnecessary re-renders (temporarily disabled)
  // const orderData = useMemo(
  //   () => ({
  //     outletId: params?.get("outletId") || "",
  //     orderAmount: subtotal,
  //     customerId: localStorage.getItem("user-id") || undefined,
  //     items: cart.map((item) => ({
  //       dishId: item._id,
  //       quantity: item.quantity || 1,
  //       price: item.price,
  //       dishName: item.name,
  //     })),
  //     foodChainId: params?.get("chainId") || undefined,
  //   }),
  //   [params, subtotal, cart]
  // );

  // const appliedOfferIds = useMemo(
  //   () => appliedOffers.map((offer) => offer._id || ""),
  //   [appliedOffers]
  // );

  // Note: Offers are now handled automatically by the backend cart system
  
  // Handle coupon application
  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    setCouponLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/coupons/validate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("user-token")}`,
          },
          body: JSON.stringify({
            code: couponCode,
            outletId: params?.get("outletId") || "",
            amount: subtotal - offerDiscount,
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        setAppliedCoupon({
          code: couponCode.toUpperCase(),
          discount: result.data.discount,
          discountType: result.data.coupon.discountType,
          discountValue: result.data.coupon.discountValue,
        });
        toast.success("Coupon applied successfully!");
        setCouponCode("");
      } else {
        toast.error(result.message || "Failed to apply coupon");
      }
    } catch (error) {
      console.error("Error applying coupon:", error);
      toast.error("An error occurred while applying the coupon");
    } finally {
      setCouponLoading(false);
    }
  };
  console.log(appliedOffers, "applied offers");
  // Remove applied coupon
  const handleRemoveCoupon = () => {
    setAppliedCoupon(null);
    toast.success("Coupon removed");
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      let response;

      if (isUpdatingOrder && orderId) {
        // Update existing order
        const itemsToUpdate = cart.map((item) => ({
          dishId: item._id,
          quantity: item.quantity || 1,
          price: item.price,
          dishName: item.name,
          addOns: (item.selectedAddOns || []).map((addOn) => ({
            addOnId: addOn.addOnId,
            name: addOn.name,
            price: addOn.price,
            quantity: addOn.quantity,
          })),
        }));

        response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/orders/${orderId}/update-items`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${localStorage.getItem("user-token")}`,
            },
            body: JSON.stringify({
              items: itemsToUpdate,
              couponCode: appliedCoupon?.code || undefined,
              couponDiscount: appliedCoupon?.discount || 0,
              appliedOffers: appliedOffers.map((offer) => ({
                _id: offer._id,
                offerId: offer.offerId,
                offerName: offer.name,
                offerType: offer.offerType,
                discount: 0, // Will be calculated by backend
              })),
            }),
          }
        );
      } else {
        // Create new order
        response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/create-order`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${localStorage.getItem("user-token")}`,
            },
            body: JSON.stringify({
              ...formData,
              items: cart,
              total: subtotal,
              outletId: params?.get("outletId") || "",
              couponCode: appliedCoupon?.code || null,
              couponDiscount: appliedCoupon?.discount || 0,
              finalAmount: total,
              appliedOffers: appliedOffers.map((offer) => ({
                _id: offer._id,
                offerId: offer.offerId,
                offerName: offer.name,
                offerType: offer.offerType,
                discount: 0, // Will be calculated by backend
              })),
            }),
          }
        );
      }

      const order = await response.json();

      if (!order.success) {
        throw new Error(
          order.message ||
            (isUpdatingOrder
              ? "Failed to update order"
              : "Failed to place order")
        );
      }

      // If order was created successfully and we have a coupon, increment its usage
      if (!isUpdatingOrder && appliedCoupon) {
        try {
          await fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/coupons/apply`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${localStorage.getItem("user-token")}`,
              },
              body: JSON.stringify({
                code: appliedCoupon.code,
                orderId: order.data._id,
              }),
            }
          );
        } catch (couponError) {
          console.error("Error applying coupon to order:", couponError);
          // Continue with order process even if coupon application fails
        }
      }

      toast.success(
        isUpdatingOrder
          ? "Order updated successfully!"
          : "Order placed successfully!"
      );

      // Emit order event
      const orderIdToTrack = isUpdatingOrder ? orderId : order.data._id;
      customerSocket?.emit("join-order", orderIdToTrack);

      clearCart(); // This now handles removing the cart from localStorage with the proper key
      localStorage.setItem("to-chat", "true");
      setPageLoading(true);
      router.push(`/order-tracking/${orderIdToTrack}`);
    } catch (error) {
      console.error("Order submission failed:", error);
      toast.error("Failed to place order. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (pageLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin">
          <Loader2 />
        </div>
      </div>
    );
  }

  if (cart.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ShoppingBag className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
          <p className="text-gray-500 mb-4">
            Add some items to proceed with checkout
          </p>
          <Button onClick={() => router.back()}>Continue Shopping</Button>
        </div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-blue-200"
      style={{
        backgroundColor: "#f8f9fa",
        overflow: "auto",
        WebkitOverflowScrolling: "touch",
        height: "auto",
        minHeight: "100vh",
      }}
    >
      <div
        className="py-4 px-4 shadow-sm sticky top-0 z-10"
        style={{ backgroundColor: theme.primaryColor }}
      >
        <div className="container mx-auto max-w-6xl flex items-center">
          <Button
            variant="ghost"
            className="text-white hover:bg-white/20"
            onClick={() => router.back()}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-xl font-bold text-white ml-4">
            {isUpdatingOrder ? "Update Order" : "Checkout"}
          </h1>
        </div>
      </div>
      <div className="container mx-auto px-4 py-6 max-w-3xl">
        <div className="space-y-6">
          {/* Main Content */}
          <div className="space-y-6">
            {/* Order Progress */}
            <div className="bg-white rounded-lg shadow-md p-4 mb-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Order Progress</h2>
                <Badge
                  className="px-3 py-1 text-white"
                  style={{ backgroundColor: theme.primaryColor }}
                >
                  Step 1 of 2
                </Badge>
              </div>
              <div className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className="w-10 h-10 rounded-full flex items-center justify-center text-white shadow-sm"
                    style={{ backgroundColor: theme.primaryColor }}
                  >
                    <CheckCircle2 size={20} />
                  </div>
                  <span className="text-xs mt-1 font-medium">Cart</span>
                </div>
                <div
                  className="flex-1 h-1 mx-2"
                  style={{ backgroundColor: theme.primaryColor }}
                ></div>
                <div className="flex flex-col items-center">
                  <div
                    className="w-10 h-10 rounded-full flex items-center justify-center text-white shadow-sm"
                    style={{ backgroundColor: theme.primaryColor }}
                  >
                    <Clock size={20} />
                  </div>
                  <span className="text-xs mt-1 font-medium">Checkout</span>
                </div>
                <div className="flex-1 h-1 mx-2 bg-gray-200"></div>
                <div className="flex flex-col items-center">
                  <div className="w-10 h-10 rounded-full flex items-center justify-center bg-gray-200 text-gray-400 shadow-sm">
                    <CheckCircle2 size={20} />
                  </div>
                  <span className="text-xs mt-1 text-gray-500">Complete</span>
                </div>
              </div>
            </div>

            {/* Quick Order Form */}
            {/* <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Quick Order Details
                </CardTitle>
                <CardDescription>
                  Enter essential information to complete your order
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm font-medium">
                      Name: {formData.name}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">
                      Phone Number: {formData.phone}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm font-medium">
                      Email: {formData.email}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium">
                      Table Number: {formData.tableNumber}
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">
                    Special Instructions: {formData.specialInstructions || "None"}
                  </div>
                </div>
              </CardContent>
            </Card> */}

            {/* Payment Method */}
            {/* <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5" />
                  Payment Method
                </CardTitle>
                <CardDescription>
                  Choose how you&apos;d like to pay
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      formData.paymentMethod === "cash" ? "border-2" : "border"
                    }`}
                    style={{
                      borderColor:
                        formData.paymentMethod === "cash"
                          ? theme.primaryColor
                          : "",
                    }}
                    onClick={() =>
                      setFormData((prev) => ({
                        ...prev,
                        paymentMethod: "cash",
                      }))
                    }
                  >
                    <div className="flex items-center mb-2">
                      <div
                        className="w-5 h-5 rounded-full border-2 mr-2 flex items-center justify-center"
                        style={{ borderColor: theme.primaryColor }}
                      >
                        {formData.paymentMethod === "cash" && (
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: theme.primaryColor }}
                          ></div>
                        )}
                      </div>
                      <span className="font-medium">Cash</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Banknote className="h-4 w-4 mr-2" />
                      <span className="text-sm">Pay on delivery</span>
                    </div>
                  </div>

                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      formData.paymentMethod === "card" ? "border-2" : "border"
                    }`}
                    style={{
                      borderColor:
                        formData.paymentMethod === "card"
                          ? theme.primaryColor
                          : "",
                    }}
                    onClick={() =>
                      setFormData((prev) => ({
                        ...prev,
                        paymentMethod: "card",
                      }))
                    }
                  >
                    <div className="flex items-center mb-2">
                      <div
                        className="w-5 h-5 rounded-full border-2 mr-2 flex items-center justify-center"
                        style={{ borderColor: theme.primaryColor }}
                      >
                        {formData.paymentMethod === "card" && (
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: theme.primaryColor }}
                          ></div>
                        )}
                      </div>
                      <span className="font-medium">Card</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <CreditCard className="h-4 w-4 mr-2" />
                      <span className="text-sm">Credit/Debit</span>
                    </div>
                  </div>

                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      formData.paymentMethod === "upi" ? "border-2" : "border"
                    }`}
                    style={{
                      borderColor:
                        formData.paymentMethod === "upi"
                          ? theme.primaryColor
                          : "",
                    }}
                    onClick={() =>
                      setFormData((prev) => ({ ...prev, paymentMethod: "upi" }))
                    }
                  >
                    <div className="flex items-center mb-2">
                      <div
                        className="w-5 h-5 rounded-full border-2 mr-2 flex items-center justify-center"
                        style={{ borderColor: theme.primaryColor }}
                      >
                        {formData.paymentMethod === "upi" && (
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: theme.primaryColor }}
                          ></div>
                        )}
                      </div>
                      <span className="font-medium">UPI</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Smartphone className="h-4 w-4 mr-2" />
                      <span className="text-sm">Google Pay/PhonePe</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card> */}

            {/* Applicable Offers - Temporarily disabled */}
            {/* <ApplicableOffers
              orderData={orderData}
              onOfferApply={handleOfferApply}
              appliedOffers={appliedOfferIds}
              className="mb-6"
            /> */}

            <Card className="shadow-md border-0">
              <CardHeader
                style={{ backgroundColor: theme.primaryColor + "15" }}
                className="rounded-t-lg"
              >
                <CardTitle className="flex items-center">
                  <ShoppingBag className="mr-2 h-5 w-5" />
                  Order Summary
                </CardTitle>
                <CardDescription>
                  {cart.reduce((sum, item) => sum + (item.quantity || 1), 0)}{" "}
                  items in your order
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="max-h-[300px] overflow-y-auto p-4 border-b">
                  {cart.map((item, index) => {
                    return (
                      <div
                        key={index}
                        className="flex justify-between items-start mb-4 last:mb-0 hover:bg-gray-50 p-2 rounded-md transition-colors"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{item.name}</span>
                            <Badge
                              variant="outline"
                              className="ml-1"
                              style={{
                                borderColor: theme.primaryColor,
                                color: theme.primaryColor,
                              }}
                            >
                              ×{item.quantity || 1}
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            ₹{item.price.toFixed(2)} each
                          </div>
                          {/* Display selected addons */}
                          {item.selectedAddOns &&
                            item.selectedAddOns.length > 0 && (
                              <div className="mt-2">
                                <div className="text-xs font-medium text-gray-600 mb-1">
                                  Add-ons:
                                </div>
                                <div className="space-y-1">
                                  {item.selectedAddOns.map(
                                    (addOn, addOnIndex) => (
                                      <div
                                        key={addOnIndex}
                                        className="text-xs text-gray-500"
                                      >
                                        • {addOn.name} x{addOn.quantity} (+₹
                                        {(addOn.price * addOn.quantity).toFixed(
                                          2
                                        )}
                                        )
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            )}
                          {/* Edit addons button */}
                          {item.enabledAddOns &&
                            item.enabledAddOns.length > 0 && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="mt-2 text-xs"
                                onClick={() => handleEditAddons(item)}
                              >
                                <Edit3 className="w-3 h-3 mr-1" />
                                {item.selectedAddOns &&
                                item.selectedAddOns.length > 0
                                  ? "Edit Add-ons"
                                  : "Add Add-ons"}
                              </Button>
                            )}
                        </div>
                        <div className="text-right">
                          {/* {addOnTotal > 0 && (
                            <div className="text-xs text-gray-500">
                              Base: ₹{(item.price * (item.quantity || 1)).toFixed(2)}
                            </div>
                          )} */}
                          <div className="font-medium">
                            {/* ₹{itemTotal.toFixed(2)} */}
                            Base: ₹
                            {(item.price * (item.quantity || 1)).toFixed(2)}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Coupon Input */}
                {/* Table Number Input */}
                <div className="p-4 border-b">
                  <div className="mb-3">
                    <label
                      htmlFor="tableNumber"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Table Number (Optional)
                    </label>
                    <Input
                      id="tableNumber"
                      placeholder="Enter table number"
                      value={formData.tableNumber}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          tableNumber: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>

                {/* Special Instructions Input */}
                <div className="p-4 border-b">
                  <div className="mb-3">
                    <label
                      htmlFor="specialInstructions"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      Special Instructions (Optional)
                    </label>
                    <textarea
                      id="specialInstructions"
                      placeholder="Any special instructions for your order..."
                      value={formData.specialInstructions}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          specialInstructions: e.target.value,
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      rows={3}
                      maxLength={500}
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      {formData.specialInstructions.length}/500 characters
                    </div>
                  </div>
                </div>

                {/* Coupon Input */}
                <div className="p-4 border-b">
                  <div className="flex items-center gap-2">
                    <div className="relative flex-1">
                      <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Enter coupon code"
                        className="pl-10"
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value)}
                        disabled={!!appliedCoupon || couponLoading}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleApplyCoupon}
                      disabled={!couponCode || couponLoading || !!appliedCoupon}
                      className="whitespace-nowrap"
                    >
                      {couponLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Applying...
                        </>
                      ) : (
                        "Apply Coupon"
                      )}
                    </Button>
                  </div>

                  {appliedCoupon && (
                    <div className="mt-3 bg-green-50 p-2 rounded-md flex justify-between items-center">
                      <div>
                        <span className="text-green-600 font-medium text-sm">
                          {appliedCoupon.code}
                        </span>
                        <span className="text-gray-600 text-xs ml-2">
                          {appliedCoupon.discountType === "percentage"
                            ? `${appliedCoupon.discountValue}% off`
                            : `₹${appliedCoupon.discountValue} off`}
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 w-7 p-0 text-gray-500 hover:text-red-500"
                        onClick={handleRemoveCoupon}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>

                <div className="p-4 space-y-3 bg-gray-50 rounded-md m-4 mb-0">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Subtotal</span>
                    <span>₹{subtotal.toFixed(2)}</span>
                  </div>

                  {/* Applied Offers Discounts */}
                  {appliedOffers.length > 0 && (
                    <>
                      {appliedOffers.map((offer, index) => (
                        <div
                          key={index}
                          className="flex justify-between text-sm"
                        >
                          <div className="flex items-center gap-2">
                            <span className="text-green-600">
                              🎉 {offer.offerName}
                            </span>
                          </div>
                          <span className="text-green-600">
                            {"discountAmount" in offer &&
                            typeof offer.discountAmount === "number"
                              ? `-₹${offer.discountAmount.toFixed(2)}`
                              : "Applied"}
                          </span>
                        </div>
                      ))}
                      {offerDiscount > 0 && (
                        <div className="flex justify-between text-sm font-medium">
                          <span className="text-green-600">
                            Total Offer Savings
                          </span>
                          <span className="text-green-600">
                            -₹{offerDiscount.toFixed(2)}
                          </span>
                        </div>
                      )}
                    </>
                  )}

                  {appliedCoupon && (
                    <div className="flex justify-between text-sm">
                      <span className="text-green-600">Coupon Discount</span>
                      <span className="text-green-600">
                        -₹{appliedCoupon.discount.toFixed(2)}
                      </span>
                    </div>
                  )}

                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span style={{ color: theme.primaryColor }}>
                      ₹{total.toFixed(2)}
                    </span>
                  </div>
                </div>

                <div className="p-4 pt-2">
                  <Button
                    className="w-full py-6 text-base font-semibold shadow-lg transition-transform hover:scale-[1.02] active:scale-[0.98] mt-4"
                    style={{ backgroundColor: theme.primaryColor }}
                    onClick={handleSubmit}
                    disabled={loading}
                  >
                    {loading
                      ? "Processing..."
                      : isUpdatingOrder
                      ? "Update Order"
                      : "Place Order Now"}
                  </Button>

                  {(!formData.name || !formData.phone) && (
                    <div className="mt-3 text-sm text-amber-600 flex items-center justify-center bg-amber-50 p-2 rounded-md">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      Your profile is incomplete. Order will use default
                      information.
                    </div>
                  )}

                  <div className="mt-4 text-center text-xs text-gray-500">
                    By placing your order, you agree to our Terms of Service and
                    Privacy Policy
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Addon Selection Dialog */}
      <Dialog
        open={isAddonDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsAddonDialogOpen(false);
            setEditingItem(null);
            setSelectedAddOns([]);
          }
        }}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {(editingItem?.selectedAddOns?.length || 0) > 0
                ? "Edit Add-ons"
                : "Add Add-ons"}{" "}
              for {editingItem?.name}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {editingItem?.enabledAddOns?.map(
              (addon: { _id: string; name: string; price: number }) => {
                const isSelected = selectedAddOns.some(
                  (sa) => sa.addOnId === addon._id
                );
                const selectedAddon = selectedAddOns.find(
                  (sa) => sa.addOnId === addon._id
                );

                return (
                  <div
                    key={addon._id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedAddOns([
                              ...selectedAddOns,
                              {
                                addOnId: addon._id,
                                name: addon.name,
                                price: addon.price,
                                quantity: 1,
                              },
                            ]);
                          } else {
                            setSelectedAddOns(
                              selectedAddOns.filter(
                                (sa) => sa.addOnId !== addon._id
                              )
                            );
                          }
                        }}
                      />
                      <div>
                        <div className="font-medium">{addon.name}</div>
                        <div className="text-sm text-gray-500">
                          ₹{addon.price}
                        </div>
                      </div>
                    </div>
                    {isSelected && (
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const updated = selectedAddOns.map((sa) =>
                              sa.addOnId === addon._id
                                ? {
                                    ...sa,
                                    quantity: Math.max(1, sa.quantity - 1),
                                  }
                                : sa
                            );
                            setSelectedAddOns(updated);
                          }}
                        >
                          -
                        </Button>
                        <span className="w-8 text-center">
                          {selectedAddon?.quantity || 1}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const updated = selectedAddOns.map((sa) =>
                              sa.addOnId === addon._id
                                ? { ...sa, quantity: sa.quantity + 1 }
                                : sa
                            );
                            setSelectedAddOns(updated);
                          }}
                        >
                          +
                        </Button>
                      </div>
                    )}
                  </div>
                );
              }
            )}
            <div className="flex space-x-2 pt-4">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => {
                  setIsAddonDialogOpen(false);
                  setEditingItem(null);
                  setSelectedAddOns([]);
                }}
              >
                Cancel
              </Button>
              <Button
                className="flex-1"
                onClick={handleSaveAddons}
                style={{ backgroundColor: theme.primaryColor }}
              >
                Save Add-ons
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CheckoutPageWrapper;
