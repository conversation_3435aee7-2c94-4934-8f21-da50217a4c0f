"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Loader2,
  Plus,
  Minus,
  ShoppingCart,
  Edit3,
  Tag,
  X,
} from "lucide-react";
import {
  updateAdminOrderItems,
  getAllDishes,
  validateCouponForOrder,
} from "@/server/admin";

interface OrderItem {
  dishId?: {
    _id: string;
    name: string;
    price: number;
    enabledAddOns?: Array<{ _id: string; name: string; price: number; type?: string }>;
  } | null;
  dishName?: string; // Stored dish name for deleted dishes
  quantity: number;
  price: number;
  addOns?: Array<{ addOnId?: string; _id?: string; name: string; price: number; quantity?: number }>;
  isServed?: boolean;
  servedQuantity?: number;
}

interface Order {
  _id: string;
  items: OrderItem[];
  totalAmount: number;
  finalAmount: number;
  paymentStatus: string;
  status: string;
  couponCode?: string;
  couponDiscount?: number;
  outletId: {
    _id: string;
    name: string;
  };
}

interface Dish {
  _id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  image?: string;
  isAvailable: boolean;
  enabledAddOns?: Array<{ _id: string; name: string; price: number; type?: string }>;
}

interface AdminOrderUpdateDialogProps {
  order: Order;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

type AddOnSel = { addOnId?: string; _id?: string; name: string; price: number; quantity?: number };


export default function AdminOrderUpdateDialog({
  order,
  isOpen,
  onClose,
  onSuccess,
}: AdminOrderUpdateDialogProps) {
  const [updatedItems, setUpdatedItems] = useState<OrderItem[]>([]);
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [couponLoading, setCouponLoading] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    discountType: string;
    discountValue: number;
  } | null>(null);

  useEffect(() => {
    if (isOpen && order) {
      // Initialize with current order items
      setUpdatedItems([...order.items]);

      // Set existing coupon if any
      if (order.couponCode && order.couponDiscount) {
        setAppliedCoupon({
          code: order.couponCode,
          discount: order.couponDiscount,
          discountType: "fixed", // Default, will be updated if needed
          discountValue: order.couponDiscount,
        });
      }

      fetchDishes();
    }
  }, [isOpen, order]);

  const fetchDishes = async () => {
    try {
      const response = await getAllDishes();
      if (response.success) {
        setDishes(response.data.filter((dish: Dish) => dish.isAvailable));
      }
    } catch (error) {
      console.error("Error fetching dishes:", error);
      toast.error("Failed to load dishes");
    }
  };

  const addDishToOrder = (dish: Dish) => {
    const existingItemIndex = updatedItems.findIndex(
      (item) => item.dishId?._id === dish._id
    );

    if (existingItemIndex >= 0) {
      const newItems = [...updatedItems];
      newItems[existingItemIndex].quantity += 1;
      setUpdatedItems(newItems);
    } else {
      const newItem: OrderItem = {
        dishId: {
          _id: dish._id,
          name: dish.name,
          price: dish.price,
        },
        dishName: dish.name,
        quantity: 1,
        price: dish.price,
        isServed: false,
      };
      setUpdatedItems([...updatedItems, newItem]);
    }
  };

  const updateItemQuantity = (itemIndex: number, newQuantity: number) => {
    const item = updatedItems[itemIndex];
    const servedQuantity = item.servedQuantity || 0;

    // If dish has served quantities, don't allow reducing quantity below served amount
    if (newQuantity < servedQuantity) {
      toast.error(
        `Cannot reduce quantity below served amount (${servedQuantity}). Current served: ${servedQuantity}, minimum allowed: ${servedQuantity}`
      );
      return;
    }

    if (newQuantity <= 0) {
      // Don't allow removing dishes with served quantities
      if (servedQuantity > 0) {
        toast.error(
          `Cannot remove dishes that have been served. This dish has ${servedQuantity} served items.`
        );
        return;
      }
      const newItems = updatedItems.filter((_, index) => index !== itemIndex);
      setUpdatedItems(newItems);
    } else {
      const newItems = [...updatedItems];
      newItems[itemIndex].quantity = newQuantity;
      setUpdatedItems(newItems);
    }
  };

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    const subtotal = calculateSubtotal();

    setCouponLoading(true);
    try {
      const response = await validateCouponForOrder({
        code: couponCode,
        outletId: order.outletId._id,
        amount: subtotal,
      });



      if (response.success) {
        setAppliedCoupon({
          code: couponCode.toUpperCase(),
          discount: response.data.discount,
          discountType: response.data.coupon.discountType,
          discountValue: response.data.coupon.discountValue,
        });
        toast.success("Coupon applied successfully!");
        setCouponCode("");
      } else {
        toast.error(response.message || "Failed to apply coupon");
      }
    } catch (error) {
      console.error("Error applying coupon:", error);
      toast.error("Failed to apply coupon");
    } finally {
      setCouponLoading(false);
    }
  };

  const removeCoupon = () => {
    setAppliedCoupon(null);
    toast.success("Coupon removed");
  };

  const calculateSubtotal = () => {
    return updatedItems.reduce((sum, item) => {
      const addOns = (item.addOns || []) as AddOnSel[];
      const addOnsPerUnit = addOns.reduce(
        (s, ao) => s + (Number(ao.price) || 0) * (Number(ao.quantity) || 1),
        0
      );
      return sum + (item.price + addOnsPerUnit) * item.quantity;
    }, 0);
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const discount = appliedCoupon ? appliedCoupon.discount : 0;
    return Math.max(0, subtotal - discount);
  };

  const handleUpdateOrder = async () => {
    if (updatedItems.length === 0) {
      toast.error("Please add at least one item to the order");
      return;
    }
    setIsLoading(true);
    try {
      const itemsToUpdate = updatedItems
        .filter((item) => item.dishId?._id) // Only include items with valid dishId
        .map((item) => ({
          dishId: item.dishId!._id,
          quantity: item.quantity,
          price: item.price,
          dishName: item.dishName,
          addOns: ((item.addOns || []) as AddOnSel[]),
        }));

      const response = await updateAdminOrderItems(
        order._id,
        itemsToUpdate,
        appliedCoupon?.code || undefined,
        undefined // Let backend recalculate the discount
      );

      if (response.success) {
        toast.success("Order updated successfully!");
        onSuccess();
        onClose();
      } else {
        toast.error("Failed to update order");
      }
    } catch (error) {
      console.error("Error updating order:", error);
      toast.error("Failed to update order");
    } finally {
      setIsLoading(false);
    }
  };

  const availableDishes = dishes.filter(
    (dish) => !updatedItems.some((item) => item.dishId?._id === dish._id)
  );

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Update Order #{order._id.slice(-6)}
          </DialogTitle>
          <DialogDescription>
            Add or remove items from this order. Changes can only be made before
            payment is completed.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Add More Items</h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {availableDishes.map((dish) => (
                <Card
                  key={dish._id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                >
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium">{dish.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {dish.description}
                        </p>
                        <p className="text-sm font-medium text-green-600">
                          ₹{dish.price}
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addDishToOrder(dish)}
                        disabled={isLoading}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>


          {/* Current Items with Add-ons */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Current Items</h3>
            {updatedItems.map((item, index) => (
              <Card key={index}>
                <CardContent className="p-4 space-y-2">
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-medium">{item.dishId?.name || item.dishName}</div>
                      <div className="text-sm text-muted-foreground">
                        ₹{(
                          item.price +
                          ((item.addOns || []) as AddOnSel[]).reduce(
                            (s, ao) => s + (Number(ao.price) || 0) * (Number(ao.quantity) || 1),
                            0
                          )
                        ).toFixed(2)} each
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="icon" onClick={() => updateItemQuantity(index, (item.quantity || 1) - 1)}><Minus className="h-4 w-4"/></Button>
                      <span>{item.quantity}</span>
                      <Button variant="outline" size="icon" onClick={() => updateItemQuantity(index, (item.quantity || 1) + 1)}><Plus className="h-4 w-4"/></Button>
                    </div>
                  </div>

                  {!!item.dishId?.enabledAddOns?.length && (
                    <div className="mt-2">
                      <div className="text-sm font-medium mb-1">Add-ons</div>
                      <div className="space-y-1">
                        {item.dishId.enabledAddOns.map((ao: { _id: string; name: string; price: number }) => {
                          const selectedAddOn = (item.addOns || []).find((s) => (s.addOnId || s._id) === ao._id);
                          const isSelected = !!selectedAddOn;
                          const quantity = selectedAddOn?.quantity || 1;

                          return (
                            <div key={ao._id} className="flex items-center justify-between border rounded p-2">
                              <div>
                                <div className="text-sm">{ao.name}</div>
                                <div className="text-xs text-muted-foreground">₹{ao.price}</div>
                              </div>
                              <div className="flex items-center gap-2">
                                {isSelected ? (
                                  <div className="flex items-center gap-2">
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      className="h-6 w-6"
                                      onClick={() => {
                                        setUpdatedItems((prev) => {
                                          const copy = [...prev];
                                          const target = { ...copy[index] } as OrderItem & { addOns?: AddOnSel[] };
                                          const list: AddOnSel[] = Array.isArray(target.addOns) ? [...target.addOns] : [];
                                          const idx = list.findIndex((s) => (s.addOnId || s._id) === ao._id);
                                          if (idx >= 0) {
                                            const newQuantity = Math.max(1, (list[idx].quantity || 1) - 1);
                                            if (newQuantity === 1) {
                                              // Remove if quantity would be 0
                                              list.splice(idx, 1);
                                            } else {
                                              list[idx] = { ...list[idx], quantity: newQuantity };
                                            }
                                          }
                                          target.addOns = list;
                                          copy[index] = target;
                                          return copy;
                                        });
                                      }}
                                    >
                                      <Minus className="h-3 w-3" />
                                    </Button>
                                    <span className="text-sm font-medium w-6 text-center">{quantity}</span>
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      className="h-6 w-6"
                                      onClick={() => {
                                        setUpdatedItems((prev) => {
                                          const copy = [...prev];
                                          const target = { ...copy[index] } as OrderItem & { addOns?: AddOnSel[] };
                                          const list: AddOnSel[] = Array.isArray(target.addOns) ? [...target.addOns] : [];
                                          const idx = list.findIndex((s) => (s.addOnId || s._id) === ao._id);
                                          if (idx >= 0) {
                                            list[idx] = { ...list[idx], quantity: (list[idx].quantity || 1) + 1 };
                                          }
                                          target.addOns = list;
                                          copy[index] = target;
                                          return copy;
                                        });
                                      }}
                                    >
                                      <Plus className="h-3 w-3" />
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        setUpdatedItems((prev) => {
                                          const copy = [...prev];
                                          const target = { ...copy[index] } as OrderItem & { addOns?: AddOnSel[] };
                                          const list: AddOnSel[] = Array.isArray(target.addOns) ? [...target.addOns] : [];
                                          const idx = list.findIndex((s) => (s.addOnId || s._id) === ao._id);
                                          if (idx >= 0) {
                                            list.splice(idx, 1);
                                          }
                                          target.addOns = list;
                                          copy[index] = target;
                                          return copy;
                                        });
                                      }}
                                    >
                                      Remove
                                    </Button>
                                  </div>
                                ) : (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      setUpdatedItems((prev) => {
                                        const copy = [...prev];
                                        const target = { ...copy[index] } as OrderItem & { addOns?: AddOnSel[] };
                                        const list: AddOnSel[] = Array.isArray(target.addOns) ? [...target.addOns] : [];
                                        list.push({ addOnId: ao._id, name: ao.name, price: ao.price, quantity: 1 });
                                        target.addOns = list;
                                        copy[index] = target;
                                        return copy;
                                      });
                                    }}
                                  >
                                    Add
                                  </Button>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Current Order Items */}
        </div>
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Current Order</h3>
            <div className="space-y-2">
              {updatedItems.map((item, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium">
                          {item.dishId?.name || item.dishName || "Deleted Dish"}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          ₹{item.price} each
                        </p>
                        {item.isServed && (
                          <Badge variant="secondary" className="mt-1">
                            Served ({item.servedQuantity || 0})
                          </Badge>
                        )}
                        {/* Display selected addons */}
                        {item.addOns && item.addOns.length > 0 && (
                          <div className="mt-2">
                            <div className="text-xs font-medium text-muted-foreground mb-1">Add-ons:</div>
                            <div className="space-y-1">
                              {item.addOns.map((addOn, addOnIndex) => (
                                <div key={addOnIndex} className="text-xs text-muted-foreground">
                                  • {addOn.name} x{addOn.quantity || 1} (₹{((addOn.price || 0) * (addOn.quantity || 1)).toFixed(2)})
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateItemQuantity(index, item.quantity - 1)
                          }
                          disabled={
                            isLoading ||
                            item.quantity <= (item.servedQuantity || 0)
                          }
                          title={
                            item.quantity <= (item.servedQuantity || 0)
                              ? `Cannot reduce quantity below served amount (${
                                  item.servedQuantity || 0
                                })`
                              : "Decrease quantity"
                          }
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                        <span className="w-8 text-center">{item.quantity}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateItemQuantity(index, item.quantity + 1)
                          }
                          disabled={isLoading}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-2 text-right">
                      {/* Calculate total including addons */}
                      {(() => {
                        const dishTotal = item.price * item.quantity;
                        const addOnsTotal = (item.addOns || []).reduce(
                          (sum, addOn) => sum + (addOn.price || 0) * (addOn.quantity || 1) ,
                          0
                        );
                        return (
                          <div>
                            <div className="text-sm text-muted-foreground">
                              Dish: ₹{dishTotal.toFixed(2)}
                              {addOnsTotal > 0 && ` + Add-ons: ₹${addOnsTotal.toFixed(2)}`}
                            </div>
                            <span className="font-medium">
                              Total: ₹{(dishTotal + addOnsTotal).toFixed(2)}
                            </span>
                          </div>
                        );
                      })()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

        {/* Coupon Section */}
        <Card>
          <CardContent className="p-4">
            <div className="space-y-4">
              <Label htmlFor="coupon">Apply Coupon</Label>

              {!appliedCoupon ? (
                <div className="flex gap-2">
                  <Input
                    id="coupon"
                    value={couponCode}
                    onChange={(e) =>
                      setCouponCode(e.target.value.toUpperCase())
                    }
                    placeholder="Enter coupon code"
                    disabled={isLoading || couponLoading}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleApplyCoupon}
                    disabled={isLoading || couponLoading || !couponCode.trim()}
                  >
                    {couponLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <>
                        <Tag className="h-4 w-4 mr-1" />
                        Apply
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800">
                      {appliedCoupon.code}
                    </span>
                    <span className="text-sm text-green-600">
                      (₹{appliedCoupon.discount} off)
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={removeCoupon}
                    disabled={isLoading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Order Summary */}
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>₹{calculateSubtotal().toFixed(2)}</span>
              </div>
              {appliedCoupon && appliedCoupon.discount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discount ({appliedCoupon.code}):</span>
                  <span>-₹{appliedCoupon.discount.toFixed(2)}</span>
                </div>
              )}
              <Separator />
              <div className="flex justify-between font-semibold">
                <span>Total:</span>
                <span>₹{calculateTotal().toFixed(2)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdateOrder}
            disabled={isLoading || updatedItems.length === 0}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <ShoppingCart className="mr-2 h-4 w-4" />
                Update Order
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
