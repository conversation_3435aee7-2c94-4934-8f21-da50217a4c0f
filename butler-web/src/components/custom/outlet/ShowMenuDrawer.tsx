/* eslint-disable @typescript-eslint/no-explicit-any */
import { Dish, SelectedAddOn } from "@/app/type";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Drawer,
  DrawerClose,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Drawer<PERSON>rigger,
} from "@/components/ui/drawer";
import { DropdownMenuLabel } from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { getOutletMenu } from "@/server/user";
import { MessageCircle, Star, X, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import useBackendCart from "@/hooks/useBackendCart";
import DishSelectionDialog from "../dishes/DishSelectionDialog";

import React, { useEffect, useState, useCallback } from "react";

const groupDishesByCategory = (dishes: Dish[]) => {
  // Create an object to hold grouped dishes
  let isFeatured = false;
  const groupedDishes: any = {
    Featured: [],
  };

  // Group dishes by their category
  dishes?.forEach((dish: any) => {
    const category = dish?.category?.name || "-";
    if (!groupedDishes[category]) {
      groupedDishes[category] = [];
    }
    if (dish?.isFeatured) {
      isFeatured = true;
      groupedDishes.Featured.push(dish);
    }
    groupedDishes[category].push(dish);
  });

  if (!isFeatured) {
    delete groupedDishes.Featured;
  }

  // Convert to array format and sort dishes within each category (featured first)
  return Object.entries(groupedDishes).map(([category, dishes]) => ({
    category,
    dishes: (dishes as any[]).sort((a, b) => {
      // Featured dishes come first
      if (a.isFeatured && !b.isFeatured) return -1;
      if (!a.isFeatured && b.isFeatured) return 1;
      // If both are featured or both are not featured, maintain original order
      return 0;
    }),
  }));
};

const ShowMenuDrawer = ({
  outletId,
  foodChainId,
  text,
}: {
  outletId: string;
  foodChainId: string;
  text: string;
}) => {
  const router = useRouter();
  const [menu, setMenu] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedDish, setSelectedDish] = useState<Dish | null>(null);
  const [isDishDialogOpen, setIsDishDialogOpen] = useState(false);

  const { addToCart: addToCartBackend } = useBackendCart();

  const fetchMenu = async () => {
    const menu = await getOutletMenu(foodChainId, outletId);
    const groupedMenu = groupDishesByCategory(menu.data);
    setMenu(groupedMenu);
  };

  useEffect(() => {
    fetchMenu();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCategorySelect = (category: any) => {
    setSelectedCategory(category);
  };

  const handleAddToCart = useCallback(async (dish: Dish) => {
    // Check if dish has addons, if yes open dialog, otherwise add directly
    if (dish.enabledAddOns && dish.enabledAddOns.length > 0) {
      setSelectedDish(dish);
      setIsDishDialogOpen(true);
    } else {
      // Add item to cart directly without addons
      await addToCartBackend(dish, 1);
    }
  }, [addToCartBackend]);

  const handleDishDialogAddToCart = useCallback(async (
    dish: Dish,
    quantity: number,
    addOns: SelectedAddOn[]
  ) => {
    // Convert SelectedAddOn to the format expected by backend
    const formattedAddOns = addOns.map((addOn) => ({
      addOnId: addOn.addOnId,
      name: addOn.name,
      price: addOn.price,
      quantity: addOn.quantity,
    }));

    await addToCartBackend(dish, quantity, formattedAddOns);
  }, [addToCartBackend]);

  const handleDishDialogClose = useCallback(() => {
    setIsDishDialogOpen(false);
    setSelectedDish(null);
  }, []);

  return (
    <Drawer direction="right">
      <DrawerTrigger asChild>
        <Button variant="outline" size="sm">
          {text}
        </Button>
      </DrawerTrigger>
      <DrawerContent className="w-full">
        <DrawerHeader>
          <DrawerTitle className="flex justify-between items-center">
            Menu
            <div className="flex gap-1 items-center">
              <Button
                size="sm"
                variant={"ghost"}
                className="cursor-pointer"
                onClick={() => {
                  localStorage.setItem("to-outlets", "true");
                  router.push(
                    `/chat?chainId=${foodChainId}&outletId=${outletId}`
                  );
                }}
              >
                <MessageCircle className="h-4 w-4" />
              </Button>

              <DrawerClose>
                <X />
              </DrawerClose>
            </div>
          </DrawerTitle>
        </DrawerHeader>
        <div className="h-16 border-b overflow-x-auto whitespace-nowrap">
          {menu.map((category, index) => (
            <button
              key={index}
              onClick={() => {
                handleCategorySelect(
                  category.category == selectedCategory ? "" : category.category
                );
                document
                  .getElementById(category.category)
                  ?.scrollIntoView({ behavior: "smooth" });
              }}
              className={`px-3 py-2 mb-1 mr-1 rounded-full text-sm whitespace-nowrap transition-colors ${
                selectedCategory === category.category
                  ? "bg-gray-200 font-medium"
                  : "bg-gray-100 hover:bg-gray-200"
              }`}
            >
              {category.category}
            </button>
          ))}
        </div>
        <ScrollArea className="h-full overflow-y-scroll">
          {menu.map((category, categoryIndex) => (
            <div
              key={categoryIndex}
              id={`category-${category.category}`}
              className={`py-2 ${
                selectedCategory && selectedCategory !== category.category
                  ? "opacity-60"
                  : ""
              }`}
            >
              <DropdownMenuLabel
                className="text-base font-bold px-4 py-2 flex justify-between items-center"
                id={category.category}
              >
                {category.category}
                <span className="text-xs text-gray-500">
                  {category.dishes.length} items
                </span>
              </DropdownMenuLabel>

              {category.dishes.map((dish: Dish, dishIndex: number) => (
                <div key={dishIndex} className="relative">
                  <div
                    className={`flex justify-between items-center px-4 py-3 hover:bg-gray-50 ${
                      dish.isFeatured
                        ? "bg-yellow-50 border-l-4 border-yellow-400"
                        : ""
                    }`}
                  >
                    <div className="flex-1">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          {dish.isFeatured && (
                            <Star className="h-4 w-4 text-yellow-500 fill-current mr-2" />
                          )}
                          <div
                            className={`font-medium ${
                              dish.isFeatured ? "text-yellow-800" : ""
                            }`}
                          >
                            {dish.name}
                          </div>
                          {dish.isFeatured && (
                            <Badge
                              variant="outline"
                              className="ml-2 bg-yellow-100 text-yellow-800 border-yellow-300 text-xs"
                            >
                              Featured
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-bold">
                            ₹{dish.price}
                          </Badge>
                          {dish.enabledAddOns && dish.enabledAddOns.length > 0 && (
                            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-600 border-blue-200">
                              Customizable
                            </Badge>
                          )}
                        </div>
                      </div>
                      {dish.description && (
                        <div className="text-xs text-gray-500 mt-1 pr-16">
                          {dish.description}
                        </div>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAddToCart(dish)}
                      className="ml-2 h-8 w-8 rounded-full p-0"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  {dishIndex < category.dishes.length - 1 && (
                    <Separator className="my-0 mx-4" />
                  )}
                </div>
              ))}

              {categoryIndex < menu.length - 1 && (
                <Separator className="my-2" />
              )}
            </div>
          ))}
        </ScrollArea>
      </DrawerContent>

      {/* Dish Selection Dialog */}
      <DishSelectionDialog
        dish={selectedDish}
        isOpen={isDishDialogOpen}
        onClose={handleDishDialogClose}
        onAddToCart={handleDishDialogAddToCart}
      />
    </Drawer>
  );
};

export default ShowMenuDrawer;
