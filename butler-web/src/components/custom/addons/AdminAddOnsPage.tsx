"use client";
import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { createAddOn, listAddOns, updateAddOn, deleteAddOn, setDishEnabledAddOns, getAllDishes } from "@/server/admin";
import { Dish } from "@/app/type";

type AddOn = { _id: string; name: string; price: number; description?: string; type?: string };

export default function AdminAddOnsPage() {
  const [addOns, setAddOns] = useState<AddOn[]>([]);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState({ name: "", description: "", price: "", type: "" });
  const [editing, setEditing] = useState<AddOn | null>(null);
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [selectedDishId, setSelectedDishId] = useState<string>("");
  const [selectedAddOnIds, setSelectedAddOnIds] = useState<string[]>([]);

  const fetchAll = async () => {
    setLoading(true);
    try {
      const res = await listAddOns();
      if (res.success) setAddOns(res.data);
      const dishesRes = await getAllDishes();
      if (dishesRes.success) setDishes(dishesRes.data);
    } catch {
      toast.error("Failed to load add-ons or dishes");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAll();
  }, []);

  const handleSave = async () => {
    if (!form.name || !form.price) {
      toast.error("Name and price are required");
      return;
    }
    const payload = { name: form.name, description: form.description, price: Number(form.price), type: form.type };
    const res = editing ? await updateAddOn(editing._id, payload) : await createAddOn(payload);
    if (res.success) {
      toast.success(editing ? "Add-on updated" : "Add-on created");
      setForm({ name: "", description: "", price: "", type: "" });
      setEditing(null);
      fetchAll();
    } else toast.error(res.message || "Failed to save add-on");
  };

  const handleDelete = async (id: string) => {
    const res = await deleteAddOn(id);
    if (res.success) {
      toast.success("Add-on deleted");
      fetchAll();
    } else toast.error(res.message || "Delete failed");
  };

  const handleAssignToDish = async () => {
    if (!selectedDishId) return toast.error("Select a dish first");
    const res = await setDishEnabledAddOns(selectedDishId, selectedAddOnIds);
    if (res.success) {
      toast.success("Assigned add-ons to dish");
      fetchAll();
    } else toast.error(res.message || "Failed to assign add-ons");
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Add-ons</h1>
      <Separator className="my-4" />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="p-4 space-y-3">
            <h2 className="font-semibold text-lg">{editing ? "Edit Add-on" : "Create Add-on"}</h2>
            <div className="grid gap-2">
              <Label>Name</Label>
              <Input value={form.name} onChange={(e) => setForm((p) => ({ ...p, name: e.target.value }))} />
            </div>
            <div className="grid gap-2">
              <Label>Description</Label>
              <Input value={form.description} onChange={(e) => setForm((p) => ({ ...p, description: e.target.value }))} />
            </div>
            <div className="grid gap-2">
              <Label>Price</Label>
              <Input type="number" value={form.price} onChange={(e) => setForm((p) => ({ ...p, price: e.target.value }))} />
            </div>
            <div className="grid gap-2">
              <Label>Type (optional)</Label>
              <Input value={form.type} onChange={(e) => setForm((p) => ({ ...p, type: e.target.value }))} />
            </div>
            <div className="flex gap-2">
              <Button onClick={handleSave} disabled={loading}>{editing ? "Update" : "Create"}</Button>
              {editing && <Button variant="outline" onClick={() => { setEditing(null); setForm({ name: "", description: "", price: "", type: "" }); }}>Cancel</Button>}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 space-y-3">
            <h2 className="font-semibold text-lg">Existing Add-ons</h2>
            <div className="space-y-2 max-h-72 overflow-auto">
              {addOns.map((ao) => (
                <div key={ao._id} className="flex items-center justify-between border rounded p-2">
                  <div>
                    <div className="font-medium">{ao.name} <span className="text-sm text-gray-500">₹{ao.price}</span></div>
                    {ao.description && <div className="text-sm text-gray-600">{ao.description}</div>}
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => { setEditing(ao); setForm({ name: ao.name, description: ao.description || "", price: String(ao.price), type: ao.type || "" }); }}>Edit</Button>
                    <Button size="sm" variant="destructive" onClick={() => handleDelete(ao._id)}>Delete</Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator className="my-6" />

      <Card>
        <CardContent className="p-4 space-y-3">
          <h2 className="font-semibold text-lg">Assign Add-ons to Dish</h2>
          <div className="grid gap-2">
            <Label>Select Dish</Label>
            <select className="border rounded p-2" value={selectedDishId} onChange={(e) => {
              const id = e.target.value; setSelectedDishId(id);
              const dish = dishes.find(d => d._id === id);
              setSelectedAddOnIds((dish?.enabledAddOns || []).map((x: { _id: string } | string) => (typeof x === 'string' ? x : x._id)));
            }}>
              <option value="">-- Select --</option>
              {dishes.map((d) => (
                <option key={d._id} value={d._id}>{d.name}</option>
              ))}
            </select>
          </div>
          <div className="grid gap-2">
            <Label>Enable Add-ons</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-56 overflow-auto">
              {addOns.map((ao) => {
                const checked = selectedAddOnIds.includes(ao._id);
                return (
                  <label key={ao._id} className="flex items-center gap-2 border rounded p-2">
                    <input type="checkbox" checked={checked} onChange={(e) => {
                      setSelectedAddOnIds((prev) => e.target.checked ? [...prev, ao._id] : prev.filter(id => id !== ao._id));
                    }} />
                    <span>{ao.name} <span className="text-sm text-gray-500">₹{ao.price}</span></span>
                  </label>
                );
              })}
            </div>
          </div>
          <Button onClick={handleAssignToDish} disabled={!selectedDishId}>Assign</Button>
        </CardContent>
      </Card>
    </div>
  );
}

