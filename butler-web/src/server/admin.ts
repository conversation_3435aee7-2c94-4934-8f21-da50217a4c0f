/* eslint-disable @typescript-eslint/no-explicit-any */
import { Category, Dish, FoodChain, Outlet } from "@/app/type";
import { getAdminToken, getAdminFoodChainId, server } from ".";

export const login = async (email: string, password: string) => {
  try {
    const response = await server.post("/v1/admin/login", { email, password });
    return response.data;
  } catch (error: any) {
    console.error("Login failed:", error);
    return error.response.data;
  }
};

export const getAllOutlets = async () => {
  const token = getAdminToken();
  try {
    const response = await server.get("/v1/admin/outlets", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    if (error.response && error.response.status === 403) {
      // Unauthorized, handle accordingly
      document.cookie =
        "auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
    }
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const getFoodChain = async () => {
  const token = getAdminToken();
  const chainId = getAdminFoodChainId();
  console.log("Getting food chain by id", chainId);
  try {
    const response = await server.get(`/v1/admin/food-chain/${chainId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const createNewOutlet = async (outlet: Outlet) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/create-outlet", outlet, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error creating outlet:", error);
    return error;
  }
};

export const updateOutlet = async (outlet: Outlet) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/outlets/${outlet._id}`,
      outlet,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating outlet:", error);
    return error;
  }
};

export const deleteOutlet = async (outletId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.delete(`/v1/admin/outlets/${outletId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error deleting outlet:", error);
    return error;
  }
};

export const updateOutletStatus = async (
  outletId: string,
  status: "active" | "inactive"
) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/outlets/${outletId}/status`,
      { status },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating outlet status:", error);
    return error;
  }
};

export const addStaffToOutlet = async (
  outletId: string,
  userId: string,
  role: string
) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/outlets/${outletId}/staff`,
      { userId, role },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding staff to outlet:", error);
    return error;
  }
};

export const removeStaffFromOutlet = async (
  outletId: string,
  staffId: string
) => {
  const token = getAdminToken();
  try {
    const response = await server.delete(
      `/v1/admin/outlets/${outletId}/staff/${staffId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error removing staff from outlet:", error);
    return error;
  }
};

export const getAllDishes = async (categoryId?: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(
      `/v1/admin/get-dishes?foodChainId=${getAdminFoodChainId()}${
        categoryId ? `&categoryId=${categoryId}` : ""
      }&includeUnavailable=true`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching dishes:", error);
    return error;
  }
};

export const createNewDish = async (dish: Dish) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/create-dish", dish, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const updateDish = async (dish: Dish) => {
  const token = getAdminToken();
  try {
    const response = await server.put(`/v1/admin/dishes/${dish._id}`, dish, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error updating dish:", error);
    return error;
  }
};

export const deleteDish = async (dishId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.delete(`/v1/admin/dishes/${dishId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error deleting dish:", error);
    return error;
  }
};

export const toggleDishFeaturedStatus = async (
  dishId: string,
  isFeatured?: boolean
) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/dishes/${dishId}/featured`,
      { isFeatured },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error toggling dish featured status:", error);
    return error;
  }
};

export const bulkUpdateDishes = async (dishes: Dish[]) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/dishes`,
      { dishes },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating dishes in bulk:", error);
    return error;
  }
};

export const addDishToOutlet = async (dishId: string, outletId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/add-dish-to-outlet`,
      { dishId, outletId },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding dish to outlet:", error);
    return error;
  }
};

export const removeDishFromOutlet = async (
  dishId: string,
  outletId: string
) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/remove-dish-from-outlet`,
      { dishId, outletId },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error removing dish from outlet:", error);
    return error;
  }
};

export const createNewCategory = async (category: Category) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/categories", category, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const getAllCategories = async () => {
  const token = getAdminToken();
  try {
    const response = await server.get(
      `/v1/admin/categories?foodChainId=${getAdminFoodChainId()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching categories:", error);
    return error;
  }
};

export const getSingleOutlet = async (outletId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(`/v1/admin/outlets/${outletId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const createChainAdmin = async (admin: {
  email: string;
  password: string;
  name: string;
  foodChainId: string;
  phone?: string;
}) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/register", admin, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error creating admin:", error);
    return error;
  }
};

export const updateChain = async (chain: FoodChain) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/food-chain/${chain._id}`,
      chain,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching layouts:", error);
    return error;
  }
};

export const getAnalytics = async (daysToAnalyze: number) => {
  const token = getAdminToken();
  try {
    const response = await server.get("/v1/admin/analytics", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        daysToAnalyze,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching analytics:", error);
    return error;
  }
};

// Staff Management Functions
export const getOutletStaff = async (outletId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(`/v1/admin/outlets/${outletId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data?.data?.staff || [];
  } catch (error) {
    console.error("Error fetching outlet staff:", error);
    return error;
  }
};

// Order Management Functions
export const getAllOrders = async () =>
  // _status?: string
  // _page = 1,
  // _limit = 50
  {
    // We're not using the token since we're not making an API call
    try {
      // Use the socket connection to get orders instead of API call
      // This is a temporary solution until the API endpoint is fixed
      // We'll return an empty array and let the socket handle the data
      return { success: true, data: [] };
    } catch (error) {
      console.error("Error fetching orders:", error);
      return { success: false, error };
    }
  };

export const updateOrderStatus = async (
  orderId: string,
  status: string,
  reason?: string
) => {
  const token = getAdminToken();
  try {
    const payload: any = { status };
    if (status === "cancelled") payload.cancellationReason = reason;
    if (status === "modified") payload.modificationReason = reason;

    const response = await server.put(
      `/v1/admin/orders/${orderId}/status`,
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating order status:", error);
    return error;
  }
};

export const deleteOrder = async (orderId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.delete(`/v1/admin/orders/${orderId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error deleting order:", error);
    return error;
  }
};

export const updateOrderPriority = async (
  orderId: string,
  priority: "low" | "normal" | "high" | "urgent"
) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/orders/${orderId}/priority`,
      { priority },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating order priority:", error);
    return error;
  }
};

export const assignOrderToStaff = async (
  orderId: string,
  userId: string,
  role: "chef" | "delivery" | "manager"
) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/orders/${orderId}/assign`,
      { userId, role },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error assigning order to staff:", error);
    return error;
  }
};

export const addKitchenNotes = async (
  orderId: string,
  kitchenNotes: string
) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/orders/${orderId}/kitchen-notes`,
      { kitchenNotes },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error adding kitchen notes:", error);
    return error;
  }
};

export const createCustomer = async (customerData: {
  name: string;
  email: string;
  phone?: string;
}) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      "/v1/admin/customers/create",
      customerData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error creating customer:", error);
    return error;
  }
};

export const createManualOrder = async (orderData: {
  outletId: string;
  customerId: string;
  items: { dishId: string; quantity: number }[];
  specialInstructions?: string;
  tableNumber?: string;
  paymentMethod?: "cash" | "online";
  couponCode?: string;
  couponDiscount?: number;
  sendInvoiceEmail?: boolean;
}) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/orders/create", orderData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error creating manual order:", error);
    return error;
  }
};

export const getAllCustomers = async (
  params: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    secondarySortBy?: string;
    secondarySortOrder?: "asc" | "desc";
    status?: string;
    city?: string;
    registrationDateFrom?: string;
    registrationDateTo?: string;
  } = {}
) => {
  const token = getAdminToken();
  try {
    const {
      page = 1,
      limit = 20,
      search = "",
      sortBy = "createdAt",
      sortOrder = "desc",
      secondarySortBy,
      secondarySortOrder = "desc",
      status,
      city,
      registrationDateFrom,
      registrationDateTo,
    } = params;

    const response = await server.get("/v1/admin/customers", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        page,
        limit,
        search,
        sortBy,
        sortOrder,
        secondarySortBy,
        secondarySortOrder,
        status,
        city,
        registrationDateFrom,
        registrationDateTo,
      },
    });
    console.log("Customer API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching customers:", error);
    return error;
  }
};

// Get all customers for order creation (no food chain filtering)
export const getAllCustomersForOrderCreation = async (
  params: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  } = {}
) => {
  const token = getAdminToken();
  try {
    const {
      page = 1,
      limit = 100,
      search = "",
      sortBy = "createdAt",
      sortOrder = "desc",
    } = params;

    const response = await server.get("/v1/admin/customers-for-order", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        page,
        limit,
        search,
        sortBy,
        sortOrder,
      },
    });
    console.log("Customers for order creation API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching customers for order creation:", error);
    return error;
  }
};

export const getAdminProfile = async () => {
  const token = getAdminToken();
  try {
    const response = await server.get("/v1/admin/profile", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching admin profile:", error);
    return error;
  }
};

export const updateAdminProfile = async (profile: any) => {
  const token = getAdminToken();
  try {
    const response = await server.put("/v1/admin/profile", profile, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error updating admin profile:", error);
    return error;
  }
};

export const updateAdminPassword = async (data: {
  oldPassword: string;
  newPassword: string;
}) => {
  const token = getAdminToken();
  try {
    const response = await server.put("/v1/admin/change-password", data, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error updating admin password:", error);
    return error;
  }
};

export const getAllAdmins = async () => {
  const token = getAdminToken();
  try {
    const response = await server.get("/v1/admin/admins", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching admins:", error);
    return error;
  }
};

export const updateCustomerStatus = async (
  customerId: string,
  status: string
) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/customers/${customerId}/status`,
      { status },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating customer status:", error);
    return error;
  }
};

export const getCustomerDetails = async (customerId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(`/v1/admin/customers/${customerId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching customer details:", error);
    return error;
  }
};

export const updateCustomerDetails = async (customerId: string, data: any) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/customers/${customerId}`,
      data,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating customer details:", error);
    return error;
  }
};

export const getOrderDetails = async (orderId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.get(`/v1/admin/orders/${orderId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching order details:", error);
    return error;
  }
};

// Employee Management
export const getAllEmployees = async (page = 1, limit = 20, search = "") => {
  const token = getAdminToken();
  try {
    const response = await server.get("/v1/admin/employees", {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        page,
        limit,
        search,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching employees:", error);
    return error;
  }
};

export const createEmployee = async (employeeData: {
  name: string;
  email: string;
  phone?: string;
  role: string;
  outletIds?: string[];
  hasPortalAccess?: boolean;
  salary?: number;
  address?: string;
  emergencyContact?: {
    name?: string;
    phone?: string;
    relation?: string;
  };
}) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/employees", employeeData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Error creating employee:", error);
    return error.response.data;
  }
};

export const updateEmployee = async (
  employeeId: string,
  employeeData: {
    name?: string;
    email?: string;
    phone?: string;
    role?: string;
    status?: string;
    outletIds?: string[];
  }
) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/employees/${employeeId}`,
      employeeData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating employee:", error);
    return error;
  }
};

export const deleteEmployee = async (employeeId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.delete(`/v1/admin/employees/${employeeId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error deleting employee:", error);
    return error;
  }
};

export const resetEmployeePassword = async (employeeId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/employees/${employeeId}/reset-password`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error resetting employee password:", error);
    return error;
  }
};

export const resetCustomerPassword = async (customerId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/customers/${customerId}/reset-password`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error resetting customer password:", error);
    return error;
  }
};

export const validateCouponForOrder = async (data: {
  code: string;
  outletId: string;
  amount: number;
}) => {
  const token = getAdminToken();
  try {
    const response = await server.post("/v1/admin/coupons/validate", data, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error validating coupon:", error);
    return { success: false, message: "Failed to validate coupon" };
  }
};

export const sendOrderInvoiceEmail = async (orderId: string) => {
  const token = getAdminToken();
  try {
    const response = await server.post(
      `/v1/admin/orders/${orderId}/send-invoice`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error sending order invoice email:", error);
    return { success: false, message: "Failed to send invoice email" };
  }
};

export const updateOrderPaymentStatus = async (
  orderId: string,
  paymentStatus: string,
  paymentMethod?: string
) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/orders/${orderId}/payment-status`,
      {
        paymentStatus,
        paymentMethod,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating order payment status:", error);
    return error;
  }
};

export const markDishAsServed = async (
  orderId: string,
  itemIndex: number,
  isServed: boolean
) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/orders/${orderId}/items/${itemIndex}/served`,
      { isServed },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating dish serving status:", error);
    return error;
  }
};

export const updateAdminOrderItems = async (
  orderId: string,
  items: any[],
  couponCode?: string,
  couponDiscount?: number
) => {
  const token = getAdminToken();
  try {
    const response = await server.put(
      `/v1/admin/orders/${orderId}/update-items`,
      {
        items,
        couponCode,
        couponDiscount,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating order items:", error);
    return error;
  }
};

// Add-ons admin APIs
export const createAddOn = async (data: { name: string; price: number; description?: string; type?: string; isActive?: boolean; }) => {
  const token = getAdminToken();
  const response = await server.post(`/v1/admin/addons`, data, { headers: { Authorization: `Bearer ${token}` }});
  return response.data;
};

export const listAddOns = async (active?: boolean) => {
  const token = getAdminToken();
  const response = await server.get(`/v1/admin/addons`, { headers: { Authorization: `Bearer ${token}` }, params: typeof active === 'boolean' ? { active } : {} });
  return response.data;
};

export const updateAddOn = async (id: string, data: any) => {
  const token = getAdminToken();
  const response = await server.put(`/v1/admin/addons/${id}`, data, { headers: { Authorization: `Bearer ${token}` }});
  return response.data;
};

export const deleteAddOn = async (id: string) => {
  const token = getAdminToken();
  const response = await server.delete(`/v1/admin/addons/${id}`, { headers: { Authorization: `Bearer ${token}` }});
  return response.data;
};

export const setDishEnabledAddOns = async (dishId: string, addOnIds: string[]) => {
  const token = getAdminToken();
  const response = await server.put(`/v1/admin/dishes/${dishId}/addons`, { addOnIds }, { headers: { Authorization: `Bearer ${token}` }});
  return response.data;
};
