import { describe, it, expect, beforeAll } from '@jest/globals';
import { getOptimizedRecommendations } from '../services/recommendation-orchestrator.js';

const mkDish = (id, name, opts={}) => ({
  _id: id,
  name,
  description: opts.description || '',
  price: opts.price ?? 200,
  category: { name: opts.category || 'Main' },
  isVeg: opts.isVeg ?? true,
  isAvailable: opts.isAvailable ?? true,
  cuisine: opts.cuisine || 'Indian',
  tags: opts.tags || [],
  ratings: opts.ratings || { average: 4.2, count: 50 },
});

const bigMenu = () => {
  const dishes = [];
  for (let i = 0; i < 300; i++) {
    dishes.push(mkDish(`veg-${i}`, `Veg Dish ${i}`, { isVeg: true, price: 100 + (i%50) }));
  }
  for (let i = 0; i < 300; i++) {
    dishes.push(mkDish(`nonveg-${i}`, `Chicken Dish ${i}`, { isVeg: false, price: 150 + (i%50), tags: ['chicken'] }));
  }
  // No desserts in this menu to test fast no-match
  return dishes;
};

const context = { outletId: 'out-1', language: 'en', lastConversation: [] };
const userId = 'user-1';

// Simulate past conversation context for preference learning
const convWithVegPreference = [
  { sender: 'butler', recommendedDishIds: ['veg-1','veg-2'], time: new Date(), aiMetadata: { responseType: 'recommendation', keywords: ['veg'] } },
];

describe('Conversation behavior: performance, dietary strictness, and context', () => {
  beforeAll(() => {
    process.env.NODE_ENV = 'test';
    process.env.FAST_TEST_EMBEDDINGS = '1';
  });

  it('fast no-match path: asks for sweet when no desserts present should respond quickly', async () => {
    const menu = bigMenu();
    const t0 = Date.now();
    const result = await getOptimizedRecommendations('show me sweet/dessert options', menu, userId, context);
    const dt = Date.now() - t0;
    expect(dt).toBeLessThan(300); // should be very fast
    expect(result.aiResponse.aiMessage.toLowerCase()).toContain("don't have any desserts".replace("'","’").split('desserts')[0]);
  });

  it('veg only: when user asks veg, only veg should be returned', async () => {
    const menu = bigMenu();
    const result = await getOptimizedRecommendations('show veg options', menu, userId, context);
    expect(result.recommendations.length).toBeGreaterThan(0);
    expect(result.recommendations.every(d => d.isVeg === true)).toBe(true);
  });

  it('non-veg only: when user asks non veg, only non-veg should be returned', async () => {
    const menu = bigMenu();
    const result = await getOptimizedRecommendations('show non-veg options', menu, userId, context);
    expect(result.recommendations.length).toBeGreaterThan(0);
    expect(result.recommendations.every(d => d.isVeg === false)).toBe(true);
  });

  it('context awareness: should use past conversation to refine recommendations', async () => {
    const menu = bigMenu();
    const ctx = { ...context, lastConversation: convWithVegPreference };
    const result = await getOptimizedRecommendations('recommend something', menu, userId, ctx);
    expect(result.recommendations.length).toBeGreaterThan(0);
  });
});

