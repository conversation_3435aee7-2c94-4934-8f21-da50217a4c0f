import AddOn from "../models/AddOn.js";
import Dish from "../models/Dish.js";

// Create add-on
export const createAddOn = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { name, description, price, type, isActive = true } = req.body;

    if (!name || typeof price !== "number") {
      return res.status(400).json({ success: false, message: "Name and price are required" });
    }

    const addOn = await AddOn.create({ name, description, price, type, isActive, foodChain: foodChainId });
    res.status(201).json({ success: true, data: addOn });
  } catch (error) {
    res.status(500).json({ success: false, message: "Failed to create add-on", error: error.message });
  }
};

// List add-ons for this food chain
export const listAddOns = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { active } = req.query;
    const query = { foodChain: foodChainId };
    if (typeof active !== "undefined") query.isActive = active === "true";
    const items = await AddOn.find(query).sort({ updatedAt: -1 });
    res.json({ success: true, data: items });
  } catch (error) {
    res.status(500).json({ success: false, message: "Failed to fetch add-ons", error: error.message });
  }
};

// Update add-on
export const updateAddOn = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { id } = req.params;
    const updates = req.body;
    const item = await AddOn.findOneAndUpdate({ _id: id, foodChain: foodChainId }, updates, { new: true });
    if (!item) return res.status(404).json({ success: false, message: "Add-on not found" });
    res.json({ success: true, data: item });
  } catch (error) {
    res.status(500).json({ success: false, message: "Failed to update add-on", error: error.message });
  }
};

// Delete add-on
export const deleteAddOn = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { id } = req.params;
    const deleted = await AddOn.findOneAndDelete({ _id: id, foodChain: foodChainId });
    if (!deleted) return res.status(404).json({ success: false, message: "Add-on not found" });
    res.json({ success: true, message: "Add-on deleted" });
  } catch (error) {
    res.status(500).json({ success: false, message: "Failed to delete add-on", error: error.message });
  }
};

// Assign enabled add-ons to a dish
export const setDishEnabledAddOns = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { dishId } = req.params;
    const { addOnIds } = req.body; // array of AddOn IDs

    if (!Array.isArray(addOnIds)) {
      return res.status(400).json({ success: false, message: "addOnIds must be an array" });
    }

    // Ensure all add-ons belong to this food chain
    const count = await AddOn.countDocuments({ _id: { $in: addOnIds }, foodChain: foodChainId });
    if (count !== addOnIds.length) {
      return res.status(400).json({ success: false, message: "Some add-ons are invalid for this food chain" });
    }

    const dish = await Dish.findOneAndUpdate(
      { _id: dishId, foodChain: foodChainId },
      { $set: { enabledAddOns: addOnIds } },
      { new: true }
    ).populate("enabledAddOns");

    if (!dish) return res.status(404).json({ success: false, message: "Dish not found" });

    res.json({ success: true, data: dish });
  } catch (error) {
    res.status(500).json({ success: false, message: "Failed to set dish add-ons", error: error.message });
  }
};

