import mongoose from "mongoose";

const cartItemSchema = new mongoose.Schema({
  dishId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Dish",
    required: true,
  },
  dishName: {
    type: String,
    required: true,
  },
  quantity: {
    type: Number,
    required: true,
    min: 1,
  },
  price: {
    type: Number,
    required: true,
    min: 0,
  },
  // Optional add-ons applied to this cart item
  addOns: [
    {
      addOnId: { type: mongoose.Schema.Types.ObjectId, ref: "AddOn" },
      name: { type: String },
      price: { type: Number, min: 0 },
      quantity: { type: Number, default: 1, min: 1 },
    },
  ],
  customizations: {
    type: Object,
    default: {},
  },
  addedAt: {
    type: Date,
    default: Date.now,
  },
});

const appliedOfferSchema = new mongoose.Schema({
  offerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Offer",
    required: true,
  },
  offerName: {
    type: String,
    required: true,
  },
  offerType: {
    type: String,
    required: true,
  },
  discountAmount: {
    type: Number,
    required: true,
    min: 0,
  },
  discountType: {
    type: String,
    enum: ["percentage", "fixed"],
    required: true,
  },
  applicableItems: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Dish",
    },
  ],
  appliedAt: {
    type: Date,
    default: Date.now,
  },
});

const appliedCouponSchema = new mongoose.Schema({
  couponId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Coupon",
    required: true,
  },
  couponCode: {
    type: String,
    required: true,
    uppercase: true,
  },
  discountAmount: {
    type: Number,
    required: true,
    min: 0,
  },
  discountType: {
    type: String,
    enum: ["percentage", "fixed"],
    required: true,
  },
  appliedAt: {
    type: Date,
    default: Date.now,
  },
});

const cartSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true,
  },
  sessionId: {
    type: String,
    index: true,
  },
  foodChainId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
    index: true,
  },
  outletId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Outlet",
    required: true,
    index: true,
  },
  items: [cartItemSchema],
  appliedOffers: [appliedOfferSchema],
  appliedCoupons: [appliedCouponSchema],
  // Calculated totals
  subtotal: {
    type: Number,
    default: 0,
    min: 0,
  },
  totalOfferDiscount: {
    type: Number,
    default: 0,
    min: 0,
  },
  totalCouponDiscount: {
    type: Number,
    default: 0,
    min: 0,
  },
  totalDiscount: {
    type: Number,
    default: 0,
    min: 0,
  },
  taxAmount: {
    type: Number,
    default: 0,
    min: 0,
  },
  deliveryFee: {
    type: Number,
    default: 0,
    min: 0,
  },
  packagingFee: {
    type: Number,
    default: 0,
    min: 0,
  },
  finalTotal: {
    type: Number,
    default: 0,
    min: 0,
  },
  // Metadata
  lastUpdated: {
    type: Date,
    default: Date.now,
  },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    index: { expireAfterSeconds: 0 },
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Create compound indices for efficient queries
cartSchema.index({ userId: 1, foodChainId: 1, outletId: 1 }, { unique: true });
cartSchema.index({ sessionId: 1, foodChainId: 1, outletId: 1 });
cartSchema.index({ lastUpdated: -1 });

// Update the updatedAt field on save
cartSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  this.lastUpdated = new Date();
  next();
});

// Calculate totals before saving
cartSchema.pre("save", function (next) {
  // Calculate subtotal including add-ons
  this.subtotal = this.items.reduce((total, item) => {
    const addOnsTotal = (item.addOns || []).reduce(
      (sum, ao) => sum + (ao.price || 0) * (ao.quantity || 1),
      0
    );
    return total + (item.price * item.quantity)  + addOnsTotal;
  }, 0);

  // Calculate total offer discount
  this.totalOfferDiscount = this.appliedOffers.reduce((total, offer) => {
    return total + offer.discountAmount;
  }, 0);

  // Calculate total coupon discount
  this.totalCouponDiscount = this.appliedCoupons.reduce((total, coupon) => {
    return total + coupon.discountAmount;
  }, 0);

  // Calculate total discount
  this.totalDiscount = this.totalOfferDiscount + this.totalCouponDiscount;

  // Calculate final total
  this.finalTotal = Math.max(
    0,
    this.subtotal -
      this.totalDiscount +
      this.taxAmount +
      this.deliveryFee +
      this.packagingFee
  );

  next();
});

// Instance methods
cartSchema.methods.addItem = function (dishData, quantity = 1, addOns = []) {
  // Try to find an existing item with SAME dish and SAME add-ons set
  const normalizeAddOns = (aos = []) =>
    JSON.stringify(
      (aos || []).map((ao) => ({
        addOnId: ao.addOnId?.toString?.() || ao.addOnId || null,
        name: ao.name || "",
        price: typeof ao.price === "number" ? ao.price : 0,
        quantity: ao.quantity || 1,
      }))
    );

  const incomingKey = normalizeAddOns(addOns);

  const existingItemIndex = this.items.findIndex((item) => {
    if (item.dishId.toString() !== dishData._id.toString()) return false;
    return normalizeAddOns(item.addOns) === incomingKey;
  });

  if (existingItemIndex >= 0) {
    // Update existing item quantity
    this.items[existingItemIndex].quantity += quantity;
  } else {
    // Add new item
    this.items.push({
      dishId: dishData._id,
      dishName: dishData.name,
      quantity: quantity,
      price: dishData.price,
      addOns: (addOns || []).map((ao) => ({
        addOnId: ao.addOnId || ao._id || null,
        name: ao.name,
        price: ao.price,
        quantity: ao.quantity || 1,
      })),
      customizations: {},
    });
  }

  return this.save();
};

cartSchema.methods.removeItem = function (dishId) {
  this.items = this.items.filter(
    (item) => item.dishId.toString() !== dishId.toString()
  );
  return this.save();
};

cartSchema.methods.updateItemQuantity = function (dishId, quantity) {
  const itemIndex = this.items.findIndex(
    (item) => item.dishId.toString() === dishId.toString()
  );

  if (itemIndex >= 0) {
    if (quantity <= 0) {
      // Remove item if quantity is 0 or negative
      this.items.splice(itemIndex, 1);
    } else {
      // Update quantity
      this.items[itemIndex].quantity = quantity;
    }
  }

  return this.save();
};

cartSchema.methods.clearItems = function () {
  this.items = [];
  this.appliedOffers = [];
  this.appliedCoupons = [];
  return this.save();
};

cartSchema.methods.clearCart = function () {
  this.items = [];
  this.appliedOffers = [];
  this.appliedCoupons = [];
  return this.save();
};

cartSchema.methods.getItemCount = function () {
  return this.items.reduce((total, item) => total + item.quantity, 0);
};

cartSchema.methods.hasItem = function (dishId) {
  return this.items.some(
    (item) => item.dishId.toString() === dishId.toString()
  );
};

cartSchema.methods.getItemQuantity = function (dishId) {
  const item = this.items.find(
    (item) => item.dishId.toString() === dishId.toString()
  );
  return item ? item.quantity : 0;
};

// Static methods
cartSchema.statics.findActiveCart = async function (
  userId,
  foodChainId,
  outletId
) {
  return await this.findOne({
    userId,
    foodChainId,
    outletId,
  }).populate([
    {
      path: "items.dishId",
      select: "name price image description isAvailable category",
      populate: {
        path: "category",
        select: "name",
      },
    },
    {
      path: "appliedOffers.offerId",
      select: "name offerType discountDetails",
    },
  ]);
};

cartSchema.statics.createOrUpdateCart = async function (
  userId,
  foodChainId,
  outletId
) {
  let cart = await this.findOne({
    userId,
    foodChainId,
    outletId,
  });

  if (!cart) {
    cart = new this({
      userId,
      foodChainId,
      outletId,
      items: [],
      appliedOffers: [],
      appliedCoupons: [],
    });
    await cart.save();
  }

  return cart;
};

export default mongoose.model("Cart", cartSchema);
