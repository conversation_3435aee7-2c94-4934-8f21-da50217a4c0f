import mongoose from "mongoose";

const addOnSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  price: { type: Number, required: true, min: 0 },
  type: { type: String }, // e.g., "extra_cheese", "sauce", etc.
  foodChain: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
    index: true,
  },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

addOnSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

export default mongoose.model("AddOn", addOnSchema);

