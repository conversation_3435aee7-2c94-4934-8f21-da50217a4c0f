import { generateEmbedding, calculateCosineSimilarity } from "./vector-service.js";
import { generateText } from "./groq-service.js";
import { recommendationCache } from "./intelligent-cache-service.js";
import { generateMenuSummary, createAIMenuContext } from "./menu-summarization-service.js";

/**
 * Enhanced RAG (Retrieval-Augmented Generation) Service
 * Improves menu search and filtering with better semantic matching
 */

// Lightweight in-memory embedding caches to avoid re-generating per request
const dishEmbeddingCache = new Map(); // key -> Float32Array/number[]
const queryEmbeddingCache = new Map(); // query -> embedding
const MAX_DISH_CACHE_ENTRIES = 5000;
const MAX_QUERY_CACHE_ENTRIES = 500;

function setCacheBounded(map, key, value, maxSize) {
  if (map.size >= maxSize) {
    const firstKey = map.keys().next().value;
    map.delete(firstKey);
  }
  map.set(key, value);
}

/**
 * Enhanced semantic search with improved RAG implementation
 */
export const enhancedSemanticRAGSearch = async (
  userQuery,
  availableDishes,
  options = {}
) => {
  try {
    const {
      maxResults = 15,
      minSimilarity = 0.2,
      useHybridSearch = true,
      contextFilters = {},
      cacheKey = null
    } = options;

    console.log(`🔍 Enhanced RAG Search: "${userQuery}" across ${availableDishes.length} dishes`);

    // Check cache first
    if (cacheKey) {
      const cached = recommendationCache.getQuery("system", cacheKey);
      if (cached) {
        console.log("📋 Using cached RAG results");
        return cached.results;
      }
    }

    // Step 0: Cheap availability/dietary filter before embeddings
    const preFiltered = availableDishes.filter((d) => {
      if (!d?.isAvailable) return false;
      if (contextFilters && typeof contextFilters.isVeg === "boolean") {
        return d.isVeg === contextFilters.isVeg;
      }
      return true;
    });

    // Step 1: Generate query embedding (with a tiny in-process cache)
    let queryEmbedding = queryEmbeddingCache.get(userQuery);
    if (!queryEmbedding) {
      queryEmbedding = await generateEmbedding(userQuery);
      setCacheBounded(queryEmbeddingCache, userQuery, queryEmbedding, MAX_QUERY_CACHE_ENTRIES);
    }

    // Step 2: Create enhanced dish representations (cache dish embeddings per id+price)
    const enhancedDishes = await Promise.all(
      preFiltered.map(async (dish) => {
        try {
          // Create comprehensive dish text for better matching
          const dishText = createComprehensiveDishText(dish);

          // Generate or reuse dish embedding (prefer persisted DB embedding if available)
          const embedKey = `${dish._id?.toString() || dish.name}-${dish.price}-${dish.isVeg}`;
          let dishEmbedding = dishEmbeddingCache.get(embedKey);

          // Try persisted embedding from DB if present
          if (!dishEmbedding && dish.embedding && Array.isArray(dish.embedding)) {
            dishEmbedding = dish.embedding;
            setCacheBounded(dishEmbeddingCache, embedKey, dishEmbedding, MAX_DISH_CACHE_ENTRIES);
          }

          if (!dishEmbedding) {
            dishEmbedding = await generateEmbedding(dishText);
            setCacheBounded(dishEmbeddingCache, embedKey, dishEmbedding, MAX_DISH_CACHE_ENTRIES);
          }

          // Calculate semantic similarity
          const semanticScore = calculateCosineSimilarity(queryEmbedding, dishEmbedding);

          // Calculate keyword-based score
          const keywordScore = calculateKeywordScore(userQuery, dish);

          // Calculate popularity score
          const popularityScore = calculatePopularityScore(dish);

          // Calculate context relevance score
          const contextScore = calculateContextScore(dish, contextFilters);

          // Combine scores with weights
          const combinedScore = (
            semanticScore * 0.5 +
            keywordScore * 0.3 +
            popularityScore * 0.1 +
            contextScore * 0.1
          );

          return {
            ...dish,
            dishText,
            semanticScore,
            keywordScore,
            popularityScore,
            contextScore,
            combinedScore,
            relevanceScore: combinedScore
          };
        } catch (error) {
          console.error(`Error processing dish ${dish._id}:`, error);
          return {
            ...dish,
            semanticScore: 0,
            keywordScore: 0,
            popularityScore: 0,
            contextScore: 0,
            combinedScore: 0,
            relevanceScore: 0
          };
        }
      })
    );

    // Step 3: Filter and sort by relevance
    const relevantDishes = enhancedDishes
      .filter(dish => dish.combinedScore >= minSimilarity)
      .sort((a, b) => b.combinedScore - a.combinedScore)
      .slice(0, maxResults);

    console.log(`✅ RAG Search found ${relevantDishes.length} relevant dishes`);
    console.log(`📊 Score distribution: Semantic avg: ${(relevantDishes.reduce((sum, d) => sum + d.semanticScore, 0) / relevantDishes.length).toFixed(3)}`);

    // Cache results
    if (cacheKey) {
      recommendationCache.cacheQuery("system", cacheKey, relevantDishes, {
        outletId: options.outletId,
        popularity: 1
      });
    }

    return {
      dishes: relevantDishes,
      searchMetadata: {
        totalProcessed: availableDishes.length,
        relevantFound: relevantDishes.length,
        averageScore: relevantDishes.reduce((sum, d) => sum + d.combinedScore, 0) / relevantDishes.length,
        searchMethod: 'enhanced_rag'
      }
    };

  } catch (error) {
    console.error("Error in enhanced RAG search:", error);
    // Fallback to basic filtering
    return {
      dishes: availableDishes.slice(0, options.maxResults || 15),
      searchMetadata: {
        totalProcessed: availableDishes.length,
        relevantFound: Math.min(availableDishes.length, options.maxResults || 15),
        averageScore: 0.5,
        searchMethod: 'fallback',
        error: true
      }
    };
  }
};

/**
 * Create comprehensive dish text for better semantic matching
 */
function createComprehensiveDishText(dish) {
  const parts = [
    dish.name,
    dish.description || '',
    dish.category?.name || '',
    dish.cuisine || '',
    ...(dish.tags || []),
    dish.isVeg ? 'vegetarian' : 'non-vegetarian',
    `price ${dish.price}`,
    dish.spiceLevel ? `spice level ${dish.spiceLevel}` : '',
    dish.ingredients?.map(ing => ing.name).join(' ') || ''
  ];

  return parts.filter(Boolean).join(' ').toLowerCase();
}

/**
 * Calculate keyword-based relevance score
 */
function calculateKeywordScore(query, dish) {
  const queryWords = query.toLowerCase().split(/\s+/);
  const dishNameLower = dish.name?.toLowerCase?.() || "";
  const descriptionLower = dish.description?.toLowerCase?.() || "";
  const categoryLower = dish.category?.name?.toLowerCase?.() || "";
  const tagsLower = (dish.tags || []).map(t => (t || '').toLowerCase());

  let score = 0;
  let totalWords = queryWords.length;

  queryWords.forEach(word => {
    if (word.length < 3) return; // Skip very short words

    // Exact match in name (highest weight)
    if (dish.name.toLowerCase().includes(word)) {
      score += 0.4;
    }
    // Match in description
    else if (dish.description?.toLowerCase().includes(word)) {
      score += 0.3;
    }
    // Match in tags or cuisine
    else if (dish.tags?.some(tag => tag.toLowerCase().includes(word)) ||
             dish.cuisine?.toLowerCase().includes(word)) {
      score += 0.2;
    }
    // Match in category
    else if (dish.category?.name?.toLowerCase().includes(word)) {
      score += 0.1;
    }
  });

  return Math.min(score / totalWords, 1.0); // Normalize to 0-1
}

/**
 * Calculate popularity score based on ratings and orders
 */
function calculatePopularityScore(dish) {
  const rating = dish.ratings?.average || 0;
  const ratingCount = dish.ratings?.count || 0;
  const orderCount = dish.orderCount || 0;

  // Normalize scores
  const ratingScore = rating / 5.0; // 0-1 scale
  const popularityBoost = Math.min((ratingCount + orderCount) / 100, 1.0); // 0-1 scale

  return (ratingScore * 0.7) + (popularityBoost * 0.3);
}

/**
 * Calculate context relevance score
 */
function calculateContextScore(dish, contextFilters) {
  let score = 0.5; // Base score

  // Dietary preferences
  if (contextFilters.isVeg !== undefined) {
    score += dish.isVeg === contextFilters.isVeg ? 0.3 : -0.3;
  }

  // Price range
  if (contextFilters.priceRange) {
    const { min = 0, max = Infinity } = contextFilters.priceRange;
    if (dish.price >= min && dish.price <= max) {
      score += 0.2;
    } else {
      score -= 0.2;
    }
  }

  // Cuisine preference
  if (contextFilters.cuisine && dish.cuisine) {
    score += dish.cuisine.toLowerCase().includes(contextFilters.cuisine.toLowerCase()) ? 0.2 : 0;
  }

  // Spice level
  if (contextFilters.spiceLevel && dish.spiceLevel) {
    const levelDiff = Math.abs(dish.spiceLevel - contextFilters.spiceLevel);
    score += Math.max(0, 0.1 - (levelDiff * 0.05));
  }

  return Math.max(0, Math.min(1, score)); // Clamp to 0-1
}

/**
 * Intelligent menu filtering using AI-powered analysis
 */
export const aiPoweredMenuFiltering = async (
  userQuery,
  availableDishes,
  options = {}
) => {
  try {
    const { maxDishes = 20, useMenuSummary = true } = options;

    if (availableDishes.length <= maxDishes) {
      return availableDishes; // No filtering needed
    }

    console.log(`🤖 AI-powered filtering: ${availableDishes.length} → ${maxDishes} dishes`);

    // Create menu summary for AI analysis
    const menuSummary = useMenuSummary ?
      await generateMenuSummary(availableDishes, {
        maxCategories: 15,
        maxDishesPerCategory: 8,
        includeDescriptions: true,
        userQuery
      }) : null;

    const prompt = `Analyze this user query and identify the most relevant dishes from the menu.

User Query: "${userQuery}"

${menuSummary ?
  `Menu Summary:\n${JSON.stringify(menuSummary, null, 2)}` :
  `Available Dishes:\n${availableDishes.slice(0, 50).map(d => `${d.name} - ${d.description || 'No description'} (₹${d.price})`).join('\n')}`
}

Instructions:
1. Select the ${maxDishes} most relevant dishes based on the user's query
2. Consider dietary preferences, cuisine types, price ranges, and specific ingredients mentioned
3. Prioritize exact matches and semantic relevance
4. Return only dish IDs in a JSON array

Format: ["dishId1", "dishId2", ...]`;

    const response = await generateText(prompt, {
      model: "llama3-70b-8192",
      max_tokens: 500,
      temperature: 0.3
    });

    try {
      const selectedIds = JSON.parse(response);
      const filteredDishes = availableDishes.filter(dish =>
        selectedIds.includes(dish._id.toString())
      );

      if (filteredDishes.length > 0) {
        console.log(`✅ AI filtering successful: ${filteredDishes.length} dishes selected`);
        return filteredDishes;
      }
    } catch (parseError) {
      console.warn("Failed to parse AI filtering response");
    }

    // Fallback to enhanced RAG search
    const ragResult = await enhancedSemanticRAGSearch(userQuery, availableDishes, {
      maxResults: maxDishes,
      minSimilarity: 0.1
    });

    return ragResult.dishes;

  } catch (error) {
    console.error("Error in AI-powered menu filtering:", error);
    return availableDishes.slice(0, options.maxDishes || 20);
  }
};

/**
 * Hybrid search combining multiple RAG techniques
 */
export const hybridRAGSearch = async (userQuery, availableDishes, options = {}) => {
  try {
    const { maxResults = 15, enableAIFilter = false, aiFilterMinSize = 120 } = options;

    // Decide whether to run the extra AI filtering step (saves tokens/latency for small menus)
    const shouldRunAIFilter = enableAIFilter && availableDishes.length >= aiFilterMinSize;

    let ragResults;
    let aiFiltered = [];

    if (shouldRunAIFilter) {
      // Run both in parallel only when explicitly enabled for large menus
      const [rag, ai] = await Promise.all([
        enhancedSemanticRAGSearch(userQuery, availableDishes, {
          maxResults: Math.ceil(maxResults * 1.5),
          ...options
        }),
        aiPoweredMenuFiltering(userQuery, availableDishes, {
          maxDishes: Math.ceil(maxResults * 1.2),
          ...options
        })
      ]);
      ragResults = rag;
      aiFiltered = ai || [];
    } else {
      // Default: rely on enhanced RAG only (no extra LLM call)
      ragResults = await enhancedSemanticRAGSearch(userQuery, availableDishes, {
        maxResults: Math.ceil(maxResults * 1.5),
        ...options
      });
      aiFiltered = [];
    }

    // Combine and deduplicate results
    const combinedResults = new Map();

    // Add RAG results with their scores
    ragResults.dishes.forEach(dish => {
      combinedResults.set(dish._id.toString(), {
        ...dish,
        hybridScore: dish.combinedScore * 0.8 // Slightly higher weight to RAG
      });
    });

    // Add AI filtered results (if any)
    aiFiltered.forEach((dish, index) => {
      const dishId = dish._id.toString();
      const aiScore = (aiFiltered.length - index) / Math.max(1, aiFiltered.length); // Position-based score

      if (combinedResults.has(dishId)) {
        // Boost score for dishes found by both methods
        const existing = combinedResults.get(dishId);
        existing.hybridScore = (existing.hybridScore + aiScore * 0.2) * 1.1;
      } else {
        combinedResults.set(dishId, {
          ...dish,
          hybridScore: aiScore * 0.2
        });
      }
    });

    // Sort by hybrid score and return top results
    const finalResults = Array.from(combinedResults.values())
      .sort((a, b) => b.hybridScore - a.hybridScore)
      .slice(0, maxResults);

    console.log(`🔄 Hybrid RAG search completed: ${finalResults.length} final results (AI filter: ${shouldRunAIFilter ? 'on' : 'off'})`);

    return {
      dishes: finalResults,
      searchMetadata: {
        totalProcessed: availableDishes.length,
        ragResults: ragResults.dishes.length,
        aiFiltered: aiFiltered.length,
        finalResults: finalResults.length,
        searchMethod: shouldRunAIFilter ? 'hybrid_rag' : 'rag_only'
      }
    };

  } catch (error) {
    console.error("Error in hybrid RAG search:", error);
    // Fallback to basic enhanced RAG
    return enhancedSemanticRAGSearch(userQuery, availableDishes, options);
  }
};
