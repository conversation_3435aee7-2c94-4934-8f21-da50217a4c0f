import {
  detectCartOperation,
  processSmartCartOperation,
} from "./cart-service.js";
import { searchDishes } from "./menu-search-service.js";
import { generateTextStream } from "./groq-service.js";
import CartOperation from "../models/CartOperation.js";

/**
 * Agentic Response Generator
 * Intelligently determines what components should be included in each response
 * based on context, operation success/failure, and user intent
 */
export class AgenticResponseGenerator {
  constructor(userId, outletId, foodChainId, language = "en") {
    this.userId = userId;
    this.outletId = outletId;
    this.foodChainId = foodChainId;
    this.language = language;
    this.context = {
      lastOperationResult: null,
      conversationHistory: [],
      cartState: null,
      userIntent: null,
      comprehensiveContext: null,
      availableDishes: [],
      userPreferences: null,
    };
  }

  /**
   * Process user message and generate agentic response
   * @param {string} message - User message
   * @param {string} conversationId - Conversation ID
   * @param {Array} lastConversation - Previous conversation history
   * @param {Array} availableDishes - Available dishes
   * @returns {Object} - Agentic response with dynamic components
   */
  async processMessage(
    message,
    conversationId,
    lastConversation = [],
    availableDishes = []
  ) {
    try {
      console.log("🤖 Processing agentic message:", message);

      // Store context for use throughout the process
      this.context.conversationHistory = lastConversation;
      this.context.availableDishes = availableDishes;

      // Get comprehensive context including user preferences, cart state, etc.
      await this.loadComprehensiveContext();

      // Initialize response structure
      const response = {
        success: true,
        components: {
          message: null,
          recommendations: [],
          faq: [],
          cartOperation: null,
          fallbackActions: [],
        },
        metadata: {
          processingSteps: [],
          confidence: 0,
          responseType: "standard",
        },
      };

      // Step 1: Analyze user intent and detect cart operations
      const intentAnalysis = await this.analyzeUserIntent(
        message,
        lastConversation
      );
      response.metadata.processingSteps.push("intent_analysis");

      // Step 2: Process cart operations if detected
      let cartOperationResult = null;
      if (intentAnalysis.cartOperation) {
        cartOperationResult = await this.processCartOperation(
          intentAnalysis.cartOperation,
          conversationId,
          message
        );
        response.components.cartOperation = cartOperationResult;
        response.metadata.processingSteps.push("cart_operation");
      }

      // Step 3: Generate contextual AI message based on operation results
      const aiMessage = await this.generateContextualMessage(
        message,
        cartOperationResult,
        intentAnalysis,
        lastConversation
      );

      // Safely extract message from AI response
      let cleanMessage = aiMessage;

      // Check if the response looks like JSON
      if (
        typeof aiMessage === "string" &&
        (aiMessage.includes("faqSuggestions") ||
          aiMessage.includes("aiMessage") ||
          aiMessage.trim().startsWith("{"))
      ) {
        try {
          // Try to parse as JSON
          const parsedResponse = JSON.parse(aiMessage);

          // Extract the actual message
          if (parsedResponse && typeof parsedResponse === "object") {
            cleanMessage =
              parsedResponse.aiMessage || parsedResponse.message || aiMessage;
          }
        } catch (parseError) {
          console.warn(
            "Failed to parse AI message as JSON:",
            parseError.message
          );
          console.warn("Raw AI message:", aiMessage);

          // Try to extract text before JSON structure
          const textBeforeJson = aiMessage.split("{")[0].trim();
          if (textBeforeJson && textBeforeJson.length > 10) {
            cleanMessage = textBeforeJson;
          } else {
            // Fallback to a clean message
            cleanMessage = "I'm here to help you with your order.";
          }
        }
      }

      // Ensure the message is always a string
      if (typeof cleanMessage !== "string") {
        cleanMessage = String(cleanMessage);
      }

      // Final cleanup: remove any remaining JSON-like structures from the message
      if (typeof cleanMessage === "string") {
        // Remove JSON objects that might be embedded in the text
        cleanMessage = cleanMessage
          .replace(/\{[^}]*"aiMessage"[^}]*\}/g, "") // Remove JSON objects containing aiMessage
          .replace(/\{[^}]*"faqSuggestions"[^}]*\}/g, "") // Remove JSON objects containing faqSuggestions
          .replace(/^\s*\{.*\}\s*$/s, "I can help you with your order.") // Replace if entire message is JSON
          .trim();

        // If message is empty after cleanup, provide fallback
        if (!cleanMessage || cleanMessage.length < 5) {
          cleanMessage = "I'm here to help you with your order.";
        }
      }

      response.components.message = cleanMessage;
      response.metadata.processingSteps.push("ai_message");

      // Step 4: Generate recommendations based on context
      const recommendations = await this.generateSmartRecommendations(
        message,
        availableDishes,
        cartOperationResult,
        intentAnalysis
      );
      response.components.recommendations = recommendations;
      response.metadata.processingSteps.push("recommendations");

      // Step 5: Generate contextual FAQ suggestions
      const faqSuggestions = await this.generateContextualFAQ(
        message,
        cartOperationResult,
        intentAnalysis,
        recommendations
      );
      response.components.faq = faqSuggestions;
      response.metadata.processingSteps.push("faq");

      // Step 6: Add fallback actions if needed
      if (cartOperationResult && !cartOperationResult.success) {
        response.components.fallbackActions = this.generateFallbackActions(
          cartOperationResult,
          intentAnalysis
        );
        response.metadata.processingSteps.push("fallback_actions");
      }

      // Step 7: Determine response type and confidence
      response.metadata.responseType = this.determineResponseType(
        cartOperationResult,
        intentAnalysis,
        recommendations
      );
      response.metadata.confidence = this.calculateConfidence(
        cartOperationResult,
        intentAnalysis,
        recommendations
      );

      console.log("✅ Agentic response generated:", {
        type: response.metadata.responseType,
        confidence: response.metadata.confidence,
        steps: response.metadata.processingSteps,
      });

      return response;
    } catch (error) {
      console.error("❌ Error in agentic response generation:", error);
      return this.generateErrorResponse(error, message);
    }
  }

  /**
   * Load comprehensive context including user preferences, cart state, and outlet menu
   */
  async loadComprehensiveContext() {
    try {
      // Import context service dynamically to avoid circular dependencies
      const { getComprehensiveContext, getUserPreferences } = await import(
        "./context-service.js"
      );

      // Get comprehensive context
      this.context.comprehensiveContext = await getComprehensiveContext(
        this.userId,
        this.outletId,
        [], // Cart history - will be fetched from database
        {
          includePersonalization: true,
          includeSession: true,
          foodChainId: this.foodChainId,
        }
      );

      // Get detailed user preferences
      this.context.userPreferences = await getUserPreferences(
        this.userId,
        this.outletId
      );

      console.log("🔍 Loaded comprehensive context:", {
        hasCartItems: !this.context.comprehensiveContext.cart.isEmpty,
        totalOrders: this.context.userPreferences?.totalOrders || 0,
        preferredCategories:
          this.context.userPreferences?.preferences?.favoriteCategories
            ?.length || 0,
        availableDishes: this.context.availableDishes.length,
      });
    } catch (error) {
      console.error("Error loading comprehensive context:", error);
      // Set default context if loading fails
      this.context.comprehensiveContext = {
        cart: { isEmpty: true, totalItems: 0, categories: [] },
        user: null,
        insights: { isReturnCustomer: false, preferredCategories: [] },
      };
      this.context.userPreferences = null;
    }
  }

  /**
   * Analyze user intent and detect cart operations
   */
  async analyzeUserIntent(message, lastConversation) {
    const lowerMessage = message.toLowerCase();

    // Detect if this is an informational query vs action-based query
    const intentType = this.classifyIntentType(lowerMessage);

    // Only detect cart operations for action-based intents
    const cartOperation =
      intentType === "action"
        ? await detectCartOperation(message, this.userId, null)
        : null;

    // Get recent cart operations for context
    const recentOperations = await CartOperation.find({
      userId: this.userId,
      createdAt: { $gte: new Date(Date.now() - 300000) }, // Last 5 minutes
    })
      .sort({ createdAt: -1 })
      .limit(3);

    return {
      cartOperation,
      intentType,
      recentOperations,
      isFollowUp: this.isFollowUpMessage(message, lastConversation),
      sentiment: this.analyzeSentiment(message),
      urgency: this.analyzeUrgency(message),
      queryCategory: this.categorizeQuery(lowerMessage),
    };
  }

  /**
   * Process cart operation with smart handling (supports multiple operations)
   */
  async processCartOperation(cartOperation, conversationId, originalMessage) {
    try {
      console.log("🛒 Processing cart operation:", {
        operation: cartOperation,
        userId: this.userId,
        conversationId,
        foodChainId: this.foodChainId,
        outletId: this.outletId,
      });

      // Handle multiple operations
      if (Array.isArray(cartOperation)) {
        return await this.processMultipleCartOperations(
          cartOperation,
          conversationId,
          originalMessage
        );
      }

      // Handle single operation
      const result = await processSmartCartOperation(
        cartOperation,
        this.userId,
        conversationId,
        this.foodChainId,
        this.outletId,
        { originalMessage, timestamp: new Date() }
      );

      console.log("🛒 Cart operation result:", {
        success: result.success,
        operation: result.operation,
        message: result.message,
        error: result.error,
      });

      // Update context with operation result
      this.context.lastOperationResult = result;

      return result;
    } catch (error) {
      console.error("❌ Error processing cart operation:", error);
      console.error("❌ Error details:", {
        message: error.message,
        stack: error.stack,
        cartOperation,
        userId: this.userId,
        conversationId,
        foodChainId: this.foodChainId,
        outletId: this.outletId,
      });

      return {
        success: false,
        message:
          "I encountered an issue processing your request. Let me help you in another way.",
        fallbackAction: "show_recommendations",
        error: error.message,
        operation: cartOperation?.operation || "unknown",
      };
    }
  }

  /**
   * Process multiple cart operations sequentially
   */
  async processMultipleCartOperations(
    operations,
    conversationId,
    originalMessage
  ) {
    const results = [];
    let overallSuccess = true;
    let combinedMessage = "";

    for (let i = 0; i < operations.length; i++) {
      const operation = operations[i];
      try {
        const result = await processSmartCartOperation(
          operation,
          this.userId,
          conversationId,
          this.foodChainId,
          this.outletId,
          { originalMessage, timestamp: new Date(), isMultiOperation: true }
        );

        results.push(result);

        if (result.success) {
          combinedMessage += result.message;
          if (i < operations.length - 1) {
            combinedMessage += " ";
          }
        } else {
          overallSuccess = false;
          combinedMessage += result.message + " ";
        }
      } catch (error) {
        console.error(`Error processing operation ${i + 1}:`, error);
        overallSuccess = false;
        combinedMessage += `Failed to process operation ${i + 1}. `;
      }
    }

    return {
      success: overallSuccess,
      message: combinedMessage.trim(),
      operations: results,
      isMultiOperation: true,
      totalOperations: operations.length,
      successfulOperations: results.filter((r) => r.success).length,
    };
  }

  /**
   * Generate contextual AI message based on operation results and intent
   */
  async generateContextualMessage(
    message,
    cartOperationResult,
    intentAnalysis,
    lastConversation
  ) {
    try {
      // If cart operation was successful, generate a contextual follow-up message
      if (cartOperationResult && cartOperationResult.success) {
        return await this.generateSuccessFollowUpMessage(
          cartOperationResult,
          message,
          intentAnalysis
        );
      }

      // If cart operation failed, generate helpful alternative message
      if (cartOperationResult && !cartOperationResult.success) {
        return await this.generateFailureRecoveryMessage(
          cartOperationResult,
          message
        );
      }

      // Generate standard AI response for non-cart operations
      return await this.generateStandardAIMessage(
        message,
        intentAnalysis,
        lastConversation
      );
    } catch (error) {
      console.error("Error generating contextual message:", error);
      return "I'm here to help you with your order. What would you like to try today?";
    }
  }

  /**
   * Generate smart recommendations based on context
   * - When the user explicitly asks for recommendations, return ONLY the recommended items
   *   without appending unrelated extras or generic menu items
   */
  async generateSmartRecommendations(
    message,
    availableDishes,
    cartOperationResult,
    intentAnalysis
  ) {
    try {
      const lowerMessage = (message || "").toLowerCase();
      const strictRecommendation =
        intentAnalysis?.queryCategory === "recommendation" ||
        /\brecommend|suggest|what.*should i try\b/.test(lowerMessage);

      // For strict recommendation requests, do not show complementary or fallback items
      if (!strictRecommendation) {
        // If cart operation failed, show dishes related to the failed operation
        if (
          cartOperationResult &&
          !cartOperationResult.success &&
          cartOperationResult.context?.searchTerm
        ) {
          return await this.searchSimilarDishes(
            cartOperationResult.context.searchTerm,
            availableDishes
          );
        }

        // If cart operation succeeded, show complementary dishes
        if (
          cartOperationResult &&
          cartOperationResult.success &&
          cartOperationResult.dish
        ) {
          return await this.getComplementaryDishes(
            cartOperationResult.dish,
            availableDishes
          );
        }
      }

      // Check for dietary preferences in the message
      const searchFilters = { availableOnly: true };

      // Enhanced vegetarian patterns
      const vegPatterns = [
        "veg option",
        "veg food",
        "vegetarian",
        "veg ",
        "veggie",
        "plant-based",
        "no meat",
        "no chicken",
        "no fish",
        "only veg",
        "pure veg",
        "veg only",
        "show me veg",
        "tell me about veg",
        "veg burger",
        "veg pizza",
        "veg dishes",
        "veg items",
        "veg menu",
        "vegetarian options",
        "vegetarian dishes",
        "vegetarian menu",
      ];

      // Enhanced non-vegetarian patterns
      const nonVegPatterns = [
        "non-veg",
        "non vegetarian",
        "meat",
        "chicken",
        "fish",
        "mutton",
        "beef",
        "pork",
        "egg",
        "non veg",
        "show me non-veg",
        "tell me about non-veg",
        "non-veg burger",
        "non-veg pizza",
        "non-veg dishes",
        "non-veg items",
        "non-veg menu",
        "non-vegetarian options",
        "non-vegetarian dishes",
        "non-vegetarian menu",
      ];

      const hasVegRequest = vegPatterns.some((pattern) =>
        lowerMessage.includes(pattern)
      );
      const hasNonVegRequest = nonVegPatterns.some((pattern) =>
        lowerMessage.includes(pattern)
      );

      // Apply strict dietary filtering
      if (hasVegRequest && !hasNonVegRequest) {
        searchFilters.vegetarian = true;
      } else if (hasNonVegRequest && !hasVegRequest) {
        searchFilters.nonVegetarian = true;
      }

      // Standard recommendation search
      const searchResults = await searchDishes(message, availableDishes, {
        limit: 5,
        threshold: 0.05, // Lower threshold for better matching
        filters: searchFilters,
      });

      // If strict mode and no results, return empty (do NOT fill with extras)
      if (strictRecommendation && searchResults.length === 0) {
        return [];
      }

      return searchResults.slice(0, 5);
    } catch (error) {
      console.error("Error generating smart recommendations:", error);
      // If strict request, do not fallback to generic menu items
      if (
        intentAnalysis?.queryCategory === "recommendation" ||
        /\brecommend|suggest|what.*should i try\b/.test((message || "").toLowerCase())
      ) {
        return [];
      }
      return availableDishes.slice(0, 5); // Fallback to first 5 dishes otherwise
    }
  }

  /**
   * Generate contextual FAQ suggestions
   */
  async generateContextualFAQ(
    message,
    cartOperationResult,
    intentAnalysis,
    recommendations
  ) {
    const baseFAQ = [
      "What's popular today?",
      "Show me vegetarian options",
      "What's in my cart?",
    ];

    // Add context-specific FAQ based on operation results
    if (cartOperationResult && cartOperationResult.success) {
      baseFAQ.unshift("What goes well with this dish?");
      baseFAQ.push("Proceed to checkout");
    }

    if (cartOperationResult && !cartOperationResult.success) {
      baseFAQ.unshift("Show me similar dishes");
      baseFAQ.push("Clear my search");
    }

    // if (recommendations.length > 0) {
    //   baseFAQ.push(`Tell me about ${recommendations[0].name}`);
    // }

    return baseFAQ.slice(0, 4); // Return top 4 suggestions
  }

  /**
   * Generate fallback actions for failed operations
   */
  generateFallbackActions(cartOperationResult, intentAnalysis) {
    const actions = [];

    if (cartOperationResult.fallbackAction === "show_recommendations") {
      actions.push({
        type: "show_recommendations",
        message: "Browse similar dishes",
        data: { searchTerm: cartOperationResult.context?.searchTerm },
      });
    }

    if (cartOperationResult.fallbackAction === "manual_add") {
      actions.push({
        type: "manual_add",
        message: "Add manually from menu",
        data: { dishName: cartOperationResult.dish?.name },
      });
    }

    if (cartOperationResult.fallbackAction === "manual_checkout") {
      actions.push({
        type: "manual_checkout",
        message: "Go to checkout page",
        data: { redirectTo: "/checkout" },
      });
    }

    if (cartOperationResult.fallbackAction === "show_menu") {
      actions.push({
        type: "show_menu",
        message: "Add items to cart first",
        data: {},
      });
    }

    actions.push({
      type: "view_menu",
      message: "Browse full menu",
      data: {},
    });

    return actions;
  }

  /**
   * Helper methods
   */
  isFollowUpMessage(message, lastConversation) {
    const followUpKeywords = ["yes", "no", "okay", "sure", "thanks", "more"];
    return (
      followUpKeywords.some((keyword) =>
        message.toLowerCase().includes(keyword)
      ) && lastConversation.length > 0
    );
  }

  analyzeSentiment(message) {
    const positiveWords = ["good", "great", "love", "like", "want", "please"];
    const negativeWords = ["bad", "hate", "don't", "not", "remove", "cancel"];

    const positive = positiveWords.filter((word) =>
      message.toLowerCase().includes(word)
    ).length;
    const negative = negativeWords.filter((word) =>
      message.toLowerCase().includes(word)
    ).length;

    if (positive > negative) return "positive";
    if (negative > positive) return "negative";
    return "neutral";
  }

  analyzeUrgency(message) {
    const urgentWords = [
      "urgent",
      "quickly",
      "fast",
      "now",
      "asap",
      "immediately",
    ];
    return urgentWords.some((word) => message.toLowerCase().includes(word))
      ? "high"
      : "normal";
  }

  determineResponseType(cartOperationResult, intentAnalysis, recommendations) {
    if (cartOperationResult && cartOperationResult.success) {
      // Special handling for order placement
      if (cartOperationResult.operation === "order") {
        return "order_success";
      }
      return "cart_success";
    }
    if (cartOperationResult && !cartOperationResult.success) {
      if (cartOperationResult.operation === "order") {
        return "order_failure";
      }
      return "cart_failure";
    }
    if (recommendations.length > 0) return "recommendation";
    return "conversational";
  }

  calculateConfidence(cartOperationResult, intentAnalysis, recommendations) {
    let confidence = 0.5; // Base confidence

    if (cartOperationResult && cartOperationResult.success) confidence += 0.3;
    if (recommendations.length > 0) confidence += 0.2;
    if (intentAnalysis.sentiment === "positive") confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  /**
   * Generate success follow-up message without duplicating the cart operation message
   */
  async generateSuccessFollowUpMessage(
    cartOperationResult,
    originalMessage,
    intentAnalysis
  ) {
    // Generate contextual follow-up based on what was added and user preferences
    const dishName = cartOperationResult.dish?.name;
    const isVeg = cartOperationResult.dish?.isVeg;
    const category = cartOperationResult.dish?.category;

    // Check user's dietary preferences
    const userVegPreference =
      this.context.userPreferences?.preferences?.vegPreference;
    const isReturnCustomer = this.context.userPreferences?.totalOrders > 0;
    const hasCartItems = !this.context.comprehensiveContext?.cart?.isEmpty;

    let followUpMessage = "";

    // Contextual responses based on what was added
    if (dishName) {
      if (category === "Appetizers" || category === "Starters") {
        followUpMessage = `Great choice with the ${dishName}! Would you like to add a main course to go with it?`;
      } else if (category === "Main Course" || category === "Mains") {
        followUpMessage = `Excellent! The ${dishName} is one of our favorites. Would you like to add some appetizers or beverages?`;
      } else if (category === "Beverages" || category === "Drinks") {
        followUpMessage = `Perfect! The ${dishName} will complement your meal nicely. Anything else to complete your order?`;
      } else if (category === "Desserts") {
        followUpMessage = `Sweet choice! The ${dishName} is a delightful way to end your meal. Ready to proceed?`;
      } else {
        followUpMessage = `Great choice with the ${dishName}! What else would you like to try?`;
      }
    } else {
      // Generic contextual responses
      if (hasCartItems) {
        followUpMessage =
          "Perfect! What else would you like to add to complete your meal?";
      } else if (isReturnCustomer) {
        followUpMessage =
          "Excellent choice! Based on your preferences, would you like me to suggest something to go with it?";
      } else {
        followUpMessage =
          "Great start! What else can I recommend from our menu?";
      }
    }

    // Add dietary preference context if relevant
    if (userVegPreference && isVeg !== undefined) {
      if (userVegPreference.vegetarian > 70 && !isVeg) {
        followUpMessage +=
          " I notice you usually prefer vegetarian options - would you like me to show you some vegetarian alternatives as well?";
      } else if (userVegPreference.nonVegetarian > 70 && isVeg) {
        followUpMessage +=
          " I see you typically enjoy non-vegetarian dishes - would you like me to suggest some protein options to go with this?";
      }
    }

    return followUpMessage;
  }

  async generateFailureRecoveryMessage(cartOperationResult, originalMessage) {
    // More contextual failure recovery based on the type of failure
    const lowerMessage = originalMessage.toLowerCase();
    const searchTerm = cartOperationResult.context?.searchTerm;
    const isReturnCustomer = this.context.userPreferences?.totalOrders > 0;
    const userVegPreference =
      this.context.userPreferences?.preferences?.vegPreference;
    const operation = cartOperationResult.operation;

    console.log("🔧 Generating failure recovery message:", {
      operation,
      searchTerm,
      error: cartOperationResult.error,
      message: cartOperationResult.message,
    });

    // Build contextual recovery message based on operation type
    let recoveryMessage = "";

    if (operation === "order") {
      // Order placement failures
      if (cartOperationResult.message?.includes("empty")) {
        recoveryMessage =
          "Your cart is empty! Please add some items before placing an order. Let me show you our menu.";
      } else {
        recoveryMessage =
          "I'm having trouble placing your order right now. Let me help you review your cart and try again.";
      }
    } else if (operation === "remove") {
      // Remove operation failures
      if (searchTerm) {
        recoveryMessage = `I couldn't find "${searchTerm}" in your cart to remove. Let me show you what's currently in your cart.`;
      } else {
        recoveryMessage =
          "I'm having trouble removing that item. Let me show you what's in your cart so you can remove items manually.";
      }
    } else if (operation === "add") {
      // Add operation failures
      if (searchTerm) {
        recoveryMessage = `I apologize, but I couldn't find "${searchTerm}" in our current menu. `;

        // Add contextual suggestions based on user preferences
        if (userVegPreference) {
          if (userVegPreference.vegetarian > 70) {
            recoveryMessage +=
              "Let me show you some delicious vegetarian options that might interest you instead.";
          } else if (userVegPreference.nonVegetarian > 70) {
            recoveryMessage +=
              "Let me suggest some excellent non-vegetarian dishes that you might enjoy.";
          } else {
            recoveryMessage +=
              "Let me show you some popular dishes from our menu that might catch your interest.";
          }
        } else if (isReturnCustomer) {
          recoveryMessage +=
            "Based on your previous orders, let me suggest some dishes you might enjoy.";
        } else {
          recoveryMessage +=
            "Let me show you some of our most popular dishes that might interest you.";
        }
      } else {
        recoveryMessage =
          "I'm having trouble adding that item. Let me show you our menu so you can add items manually.";
      }
    } else {
      // Generic failure recovery
      if (lowerMessage.includes("more")) {
        recoveryMessage =
          "I'd be happy to show you more options! Let me suggest some dishes based on your preferences.";
      } else {
        recoveryMessage =
          "I apologize for the confusion. Let me help you explore our menu and find something delicious.";
      }
    }

    // Add encouraging closing
    if (isReturnCustomer) {
      recoveryMessage +=
        " I remember your preferences and I'm confident we can find something perfect for you!";
    } else {
      recoveryMessage += " I'm here to help you discover our amazing dishes!";
    }

    return recoveryMessage;
  }

  /**
   * Classify intent type: informational vs action-based
   */
  classifyIntentType(lowerMessage) {
    // Informational query patterns
    const informationalPatterns = [
      /what.*(?:is|are|best|popular|good|available|special|featured)/,
      /tell me about/,
      /show me/,
      /which.*(?:is|are)/,
      /how.*(?:is|are|much|many)/,
      /do you have/,
      /what.*goes well with/,
      /what.*recommend/,
      /what.*suggest/,
      /describe/,
      /explain/,
      /list/,
      /menu/,
      /options/,
      /choices/,
      /vegetarian/,
      /vegan/,
      /spicy/,
      /mild/,
      /ingredients/,
      /allergens/,
      /nutritional/,
      /calories/,
      /price/,
      /cost/,
    ];

    // Action-based query patterns
    const actionPatterns = [
      /add/,
      /remove/,
      /delete/,
      /put.*in.*cart/,
      /take.*out/,
      /i want/,
      /i would like/,
      /i'll take/,
      /give me/,
      /order/,
      /buy/,
      /purchase/,
      /get me/,
      /clear.*cart/,
      /empty.*cart/,
      /checkout/,
      /proceed/,
    ];

    // Check for informational patterns first
    for (const pattern of informationalPatterns) {
      if (pattern.test(lowerMessage)) {
        return "information";
      }
    }

    // Check for action patterns
    for (const pattern of actionPatterns) {
      if (pattern.test(lowerMessage)) {
        return "action";
      }
    }

    // Default to information for ambiguous queries
    return "information";
  }

  /**
   * Categorize the query for better response generation
   */
  categorizeQuery(lowerMessage) {
    if (/best|popular|recommend|suggest|top|favorite/.test(lowerMessage)) {
      return "recommendation";
    }
    if (
      /vegetarian|vegan|gluten.*free|dairy.*free|allergen/.test(lowerMessage)
    ) {
      return "dietary";
    }
    if (/price|cost|cheap|expensive|budget/.test(lowerMessage)) {
      return "pricing";
    }
    if (/spicy|mild|hot|sweet|sour|salty/.test(lowerMessage)) {
      return "taste";
    }
    if (/cart|order|checkout/.test(lowerMessage)) {
      return "cart";
    }
    return "general";
  }

  async generateStandardAIMessage(message, intentAnalysis, lastConversation) {
    try {
      // Create comprehensive butler prompt with full context
      const prompt = await this.createButlerPrompt(message, lastConversation);

      let fullResponse = "";
      await generateTextStream(
        prompt,
        (chunk) => {
          fullResponse += chunk;
        },
        { model: "llama3-70b-8192", max_tokens: 200, temperature: 0.7 }
      );

      try {
        const parsed = JSON.parse(fullResponse);
        let aiMessage =
          parsed.aiMessage ||
          parsed.message ||
          "I'm here to help you explore our delicious menu!";

        // Ensure the message is always a string
        if (typeof aiMessage === "object") {
          aiMessage =
            aiMessage.aiMessage ||
            aiMessage.message ||
            JSON.stringify(aiMessage);
        }
        if (typeof aiMessage !== "string") {
          aiMessage = String(aiMessage);
        }

        return aiMessage;
      } catch (parseError) {
        console.error("Error parsing AI response:", parseError);
        // Return the raw response if JSON parsing fails, but clean it up
        let cleanResponse = fullResponse.replace(/```json|```/g, "").trim();
        if (!cleanResponse) {
          cleanResponse = this.generateFallbackMessage(message);
        }
        return cleanResponse;
      }
    } catch (error) {
      console.error("Error generating standard AI message:", error);
      return this.generateFallbackMessage(message);
    }
  }

  /**
   * Create comprehensive butler prompt with full context
   */
  async createButlerPrompt(message, lastConversation) {
    // Get language instructions
    const { getLanguageInstructions } = await import("./butler-service.js");
    const languageInstructions = getLanguageInstructions(this.language);

    // Prepare menu data
    const menuData = JSON.stringify(this.context.availableDishes);
    const availableDishNames = this.context.availableDishes
      .map((dish) => dish.name)
      .filter((name) => name);

    // Generate context prompt
    let contextPrompt = "";
    if (this.context.comprehensiveContext) {
      const { generateContextPrompt } = await import("./context-service.js");
      contextPrompt = generateContextPrompt(this.context.comprehensiveContext);
    }

    // Build user preference context
    let userPreferenceContext = "";
    if (
      this.context.userPreferences &&
      this.context.userPreferences.totalOrders > 0
    ) {
      const prefs = this.context.userPreferences.preferences;
      userPreferenceContext = `\n\nUSER PREFERENCE HISTORY:
- Total previous orders: ${this.context.userPreferences.totalOrders}
- Favorite categories: ${
        prefs.favoriteCategories?.map((c) => c.category).join(", ") ||
        "None yet"
      }
- Favorite cuisines: ${
        prefs.favoriteCuisines?.map((c) => c.cuisine).join(", ") || "None yet"
      }
- Dietary preference: ${
        prefs.vegPreference?.vegetarian > prefs.vegPreference?.nonVegetarian
          ? "Mostly Vegetarian"
          : prefs.vegPreference?.nonVegetarian > prefs.vegPreference?.vegetarian
          ? "Mostly Non-Vegetarian"
          : "Mixed"
      }
- Price preference: ${
        prefs.pricePreference?.high > 50
          ? "Premium"
          : prefs.pricePreference?.low > 50
          ? "Budget-friendly"
          : "Mid-range"
      }`;
    } else {
      userPreferenceContext =
        "\n\nUSER PREFERENCE HISTORY:\n- This appears to be a new customer with no previous orders.";
    }

    const prompt = `You are an experienced, knowledgeable butler at this specific restaurant outlet. You have intimate knowledge of every dish on our menu, understand our customers' preferences, and provide personalized, contextual responses.

${languageInstructions}

OUTLET'S COMPLETE MENU (you know every dish intimately):
${menuData}

AVAILABLE DISH NAMES (reference these specifically):
${availableDishNames.map((name) => `- ${name}`).join("\n")}

CONVERSATION HISTORY:
${JSON.stringify(lastConversation)}

${contextPrompt}

${userPreferenceContext}

BUTLER PERSONA GUIDELINES:
- You are the butler of THIS specific outlet and know every dish personally
- Reference specific dishes from our menu when relevant
- Consider the user's order history and preferences
- Be warm, professional, and knowledgeable about our offerings
- If user asks about dishes not on our menu, politely clarify what we actually offer
- Always suggest alternatives from our actual menu
- Be contextually aware of their cart contents and previous conversations

DIETARY PREFERENCE HANDLING:
- CRITICAL: Always check the "isVeg" field in dish data (true = vegetarian, false = non-vegetarian)
- If user asks for "veg" or "vegetarian" options, ONLY suggest dishes with "isVeg": true
- If user asks for "non-veg" or "non-vegetarian" options, ONLY suggest dishes with "isVeg": false
- Consider user's historical dietary preferences from their order history
- If user has strong vegetarian preference (>70%), prioritize vegetarian dishes
- If user has strong non-vegetarian preference (>70%), include protein-rich options
- Always mention dietary type when recommending dishes (e.g., "This delicious vegetarian curry" or "Our signature non-veg biryani")

RESPONSE FORMAT:
Respond with a JSON object containing:
{
  "aiMessage": "Your contextual, butler-like response as a string",
  "keywords": ["relevant", "menu", "keywords"],
  "detectedLanguage": "${this.language}"
}

User's current message: "${message}"

Provide a helpful, contextual response that demonstrates your knowledge of our menu and the user's preferences.`;

    return prompt;
  }

  /**
   * Generate fallback message when AI generation fails
   */
  generateFallbackMessage(message) {
    const lowerMessage = message.toLowerCase();

    // Check if user is asking about specific dishes
    if (lowerMessage.includes("menu") || lowerMessage.includes("dish")) {
      return "I'd be happy to help you explore our menu! Let me show you some of our popular dishes.";
    }

    // Check for dietary preferences
    if (lowerMessage.includes("veg")) {
      return "I can help you find some delicious vegetarian options from our menu!";
    }

    if (
      lowerMessage.includes("non-veg") ||
      lowerMessage.includes("chicken") ||
      lowerMessage.includes("meat")
    ) {
      return "I can suggest some excellent non-vegetarian dishes from our menu!";
    }

    // Check for cart-related queries
    if (lowerMessage.includes("cart") || lowerMessage.includes("order")) {
      return "I'm here to help you with your order! What would you like to add to your cart?";
    }

    // Default contextual response
    if (this.context.userPreferences?.totalOrders > 0) {
      return "Welcome back! I remember your preferences. What would you like to try today?";
    }

    return "Welcome! I'm your personal butler here to help you discover our delicious menu. What can I recommend for you today?";
  }

  async searchSimilarDishes(searchTerm, availableDishes) {
    try {
      // Enhanced search with context awareness
      console.log(`🔍 Searching for similar dishes to: "${searchTerm}"`);

      // First, try to find exact or close matches with higher threshold
      let results = await searchDishes(searchTerm, availableDishes, {
        limit: 5,
        threshold: 0.3, // Higher threshold for better quality results
        filters: { availableOnly: true },
      });

      // If no good results, try with recent cart context
      if (results.length === 0) {
        console.log(
          "🔄 No good matches found, trying with recent cart context"
        );

        // Get recent cart operations for context
        const recentOperations = await CartOperation.find({
          userId: this.userId,
          status: "completed",
          operation: "add",
          createdAt: { $gte: new Date(Date.now() - 600000) }, // Last 10 minutes
        })
          .sort({ createdAt: -1 })
          .limit(3)
          .populate("dishId", "name category cuisine tags");

        // If we have recent operations, search for dishes in similar categories
        if (recentOperations.length > 0) {
          const recentCategories = recentOperations
            .map((op) => op.dishId?.category?.name)
            .filter(Boolean);

          const recentCuisines = recentOperations
            .map((op) => op.dishId?.cuisine)
            .filter(Boolean);

          // Search for dishes in similar categories or cuisines
          results = availableDishes
            .filter((dish) => {
              return (
                recentCategories.includes(dish.category?.name) ||
                recentCuisines.includes(dish.cuisine) ||
                dish.isPopular ||
                dish.isFeatured
              );
            })
            .slice(0, 5);

          console.log(`🎯 Found ${results.length} contextual recommendations`);
        }
      }

      // Final fallback to popular/featured dishes
      if (results.length === 0) {
        results = availableDishes
          .filter(
            (dish) =>
              dish.isPopular || dish.isFeatured || dish.ratings?.average > 4
          )
          .slice(0, 5);
      }

      return results.length > 0 ? results : availableDishes.slice(0, 5);
    } catch (error) {
      console.error("Error in searchSimilarDishes:", error);
      return availableDishes.slice(0, 5);
    }
  }

  async getComplementaryDishes(dish, availableDishes) {
    // Simple complementary logic - same category or popular items
    const sameCategoryDishes = availableDishes.filter(
      (d) =>
        d.category &&
        dish.category &&
        d.category._id.toString() === dish.category._id.toString() &&
        d._id.toString() !== dish._id.toString()
    );

    return sameCategoryDishes
      .slice(0, 3)
      .concat(
        availableDishes.filter((d) => d.isPopular || d.isFeatured).slice(0, 2)
      );
  }

  generateErrorResponse(error, message) {
    return {
      success: false,
      components: {
        message:
          "I'm having trouble processing your request right now. Let me show you our menu instead.",
        recommendations: [],
        faq: ["Show me the menu", "What's popular?", "Help"],
        cartOperation: null,
        fallbackActions: [
          {
            type: "show_menu",
            message: "Browse menu",
            data: {},
          },
        ],
      },
      metadata: {
        processingSteps: ["error_handling"],
        confidence: 0.1,
        responseType: "error",
        error: error.message,
      },
    };
  }
}
