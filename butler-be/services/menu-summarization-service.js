import { generateText } from "./groq-service.js";
import { recommendationCache } from "./intelligent-cache-service.js";

/**
 * Generate a concise menu summary for AI processing
 * This reduces token usage by 70-80% compared to sending full dish details
 */
export const generateMenuSummary = async (dishes, options = {}) => {
  try {
    const {
      maxCategories = 10,
      maxDishesPerCategory = 5,
      includeDescriptions = false,
      userQuery = "",
      cacheKey = null
    } = options;

    // Check cache first
    if (cacheKey) {
      const cached = recommendationCache.getQuery("system", cacheKey);
      if (cached) {
        console.log("📋 Using cached menu summary");
        return cached.results;
      }
    }

    // Group dishes by category
    const categorizedDishes = groupDishesByCategory(dishes);
    
    // Create concise summary
    const summary = {
      totalDishes: dishes.length,
      categories: [],
      priceRange: calculatePriceRange(dishes),
      cuisineTypes: extractUniqueCuisines(dishes),
      dietaryOptions: analyzeDietaryOptions(dishes)
    };

    // Process each category
    for (const [categoryName, categoryDishes] of Object.entries(categorizedDishes)) {
      if (summary.categories.length >= maxCategories) break;

      const topDishes = categoryDishes
        .sort((a, b) => (b.ratings?.average || 0) - (a.ratings?.average || 0))
        .slice(0, maxDishesPerCategory);

      const categoryInfo = {
        name: categoryName,
        count: categoryDishes.length,
        dishes: topDishes.map(dish => ({
          id: dish._id,
          name: dish.name,
          price: dish.price,
          isVeg: dish.isVeg,
          cuisine: dish.cuisine,
          rating: dish.ratings?.average || 0,
          ...(includeDescriptions && dish.description && {
            description: dish.description.substring(0, 50) + "..."
          })
        }))
      };

      summary.categories.push(categoryInfo);
    }

    // Cache the summary
    if (cacheKey) {
      recommendationCache.cacheQuery("system", cacheKey, summary, {
        outletId: options.outletId,
        popularity: 1
      });
    }

    console.log(`📋 Generated menu summary: ${dishes.length} dishes → ${JSON.stringify(summary).length} chars`);
    return summary;

  } catch (error) {
    console.error("Error generating menu summary:", error);
    // Fallback to basic summary
    return createBasicSummary(dishes);
  }
};

/**
 * Create an AI-optimized menu context string
 * This is what gets sent to the AI instead of full dish objects
 */
export const createAIMenuContext = (menuSummary, userQuery = "") => {
  try {
    const context = {
      overview: {
        totalDishes: menuSummary.totalDishes,
        priceRange: `₹${menuSummary.priceRange.min}-${menuSummary.priceRange.max}`,
        cuisines: menuSummary.cuisineTypes.slice(0, 5),
        dietary: menuSummary.dietaryOptions
      },
      categories: menuSummary.categories.map(cat => ({
        name: cat.name,
        count: cat.count,
        topDishes: cat.dishes.map(dish => 
          `${dish.name} (₹${dish.price}, ${dish.isVeg ? 'Veg' : 'Non-Veg'}${dish.rating > 0 ? `, ${dish.rating}★` : ''})`
        )
      }))
    };

    return JSON.stringify(context);
  } catch (error) {
    console.error("Error creating AI menu context:", error);
    return JSON.stringify({ error: "Menu context unavailable" });
  }
};

/**
 * Generate intelligent menu summary using AI
 * For very large menus, use AI to create smart summaries
 */
export const generateIntelligentMenuSummary = async (dishes, userQuery, options = {}) => {
  try {
    if (dishes.length < 50) {
      // For smaller menus, use direct summarization
      return generateMenuSummary(dishes, options);
    }

    const { maxTokens = 500 } = options;
    
    // Create basic summary first
    const basicSummary = await generateMenuSummary(dishes, { 
      ...options, 
      maxCategories: 15,
      maxDishesPerCategory: 3 
    });

    // Use AI to create contextual summary based on user query
    const prompt = `Create a concise menu summary focused on the user's query: "${userQuery}"

Available Menu Data:
${JSON.stringify(basicSummary, null, 2)}

Instructions:
1. Prioritize categories and dishes most relevant to the user's query
2. Include price ranges and dietary options
3. Mention popular/highly-rated items
4. Keep response under ${maxTokens} tokens
5. Format as structured JSON

Return only the JSON summary, no other text.`;

    const aiSummary = await generateText(prompt, {
      model: "llama3-70b-8192",
      max_tokens: maxTokens,
      temperature: 0.3
    });

    try {
      const parsedSummary = JSON.parse(aiSummary);
      console.log(`🤖 Generated AI menu summary: ${dishes.length} dishes → ${aiSummary.length} chars`);
      return parsedSummary;
    } catch (parseError) {
      console.warn("Failed to parse AI summary, using basic summary");
      return basicSummary;
    }

  } catch (error) {
    console.error("Error in intelligent menu summarization:", error);
    return generateMenuSummary(dishes, options);
  }
};

/**
 * Helper functions
 */
function groupDishesByCategory(dishes) {
  const grouped = {};
  
  dishes.forEach(dish => {
    const categoryName = dish.category?.name || "Uncategorized";
    if (!grouped[categoryName]) {
      grouped[categoryName] = [];
    }
    grouped[categoryName].push(dish);
  });

  return grouped;
}

function calculatePriceRange(dishes) {
  const prices = dishes.map(dish => dish.price).filter(price => price > 0);
  return {
    min: Math.min(...prices) || 0,
    max: Math.max(...prices) || 0,
    average: Math.round(prices.reduce((sum, price) => sum + price, 0) / prices.length) || 0
  };
}

function extractUniqueCuisines(dishes) {
  const cuisines = [...new Set(dishes.map(dish => dish.cuisine).filter(Boolean))];
  return cuisines.slice(0, 8); // Limit to top 8 cuisines
}

function analyzeDietaryOptions(dishes) {
  const vegCount = dishes.filter(dish => dish.isVeg).length;
  const nonVegCount = dishes.length - vegCount;
  
  return {
    vegetarian: vegCount,
    nonVegetarian: nonVegCount,
    percentage: {
      veg: Math.round((vegCount / dishes.length) * 100),
      nonVeg: Math.round((nonVegCount / dishes.length) * 100)
    }
  };
}

function createBasicSummary(dishes) {
  return {
    totalDishes: dishes.length,
    categories: ["Mixed Items"],
    priceRange: calculatePriceRange(dishes),
    cuisineTypes: extractUniqueCuisines(dishes),
    dietaryOptions: analyzeDietaryOptions(dishes),
    dishes: dishes.slice(0, 10).map(dish => ({
      id: dish._id,
      name: dish.name,
      price: dish.price,
      isVeg: dish.isVeg
    }))
  };
}

/**
 * Get past recommendations from conversation history
 */
export const extractPastRecommendations = (conversationHistory = []) => {
  try {
    const pastRecommendations = [];
    
    conversationHistory.forEach(message => {
      if (message.sender === 'butler' && message.recommendedDishIds?.length > 0) {
        pastRecommendations.push({
          timestamp: message.time,
          dishIds: message.recommendedDishIds,
          query: message.aiMetadata?.keywords || [],
          responseType: message.aiMetadata?.responseType || 'recommendation'
        });
      }
    });

    return pastRecommendations.slice(-5); // Return last 5 recommendation sets
  } catch (error) {
    console.error("Error extracting past recommendations:", error);
    return [];
  }
};
